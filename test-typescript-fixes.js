#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔧 Testing TypeScript Compilation Fixes');
console.log('=====================================\n');

// Check TypeScript compilation
console.log('📝 Checking TypeScript compilation...');
try {
  execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.log('❌ TypeScript compilation failed:');
  console.log(error.stdout?.toString() || error.message);
  console.log('\n🔍 Detailed error output:');
  console.log(error.stderr?.toString() || 'No stderr output');
}

// Test specific files that were problematic
const testFiles = [
  'tests/unit/imports.test.ts',
  'tests/simple.test.ts',
  'tests/unit/shared/utils/crypto.test.ts',
  'tests/unit/shared/utils/validation.test.ts',
];

console.log('\n🧪 Testing specific files...');
for (const file of testFiles) {
  if (fs.existsSync(file)) {
    try {
      console.log(`Testing ${file}...`);
      execSync(`npx vitest run ${file} --reporter=verbose`, { 
        stdio: 'pipe',
        timeout: 30000 
      });
      console.log(`✅ ${file} - PASSED`);
    } catch (error) {
      console.log(`❌ ${file} - FAILED`);
      console.log(error.stdout?.toString() || error.message);
    }
  } else {
    console.log(`⚠️  ${file} - FILE NOT FOUND`);
  }
}

// Test mock factory imports
console.log('\n🏭 Testing mock factory imports...');
try {
  execSync('npx vitest run tests/unit/imports.test.ts --reporter=verbose', { 
    stdio: 'pipe',
    timeout: 30000 
  });
  console.log('✅ Mock factory imports - PASSED');
} catch (error) {
  console.log('❌ Mock factory imports - FAILED');
  console.log(error.stdout?.toString() || error.message);
}

// Test service imports
console.log('\n🔧 Testing service compilation...');
const serviceFiles = [
  'src/application/services/WhatsAppService.ts',
  'src/application/services/PaymentService.ts',
  'src/application/services/FlowService.ts',
  'src/application/services/UserService.ts',
];

for (const file of serviceFiles) {
  if (fs.existsSync(file)) {
    try {
      execSync(`npx tsc --noEmit ${file}`, { stdio: 'pipe' });
      console.log(`✅ ${file} - COMPILED`);
    } catch (error) {
      console.log(`❌ ${file} - COMPILATION FAILED`);
      console.log(error.stdout?.toString() || error.message);
    }
  } else {
    console.log(`⚠️  ${file} - FILE NOT FOUND`);
  }
}

console.log('\n📊 Summary:');
console.log('- Fixed missing mock factory file');
console.log('- Updated service test interfaces to match actual implementations');
console.log('- Fixed import paths in test files');
console.log('- Aligned tests with Cloudflare Workers testing patterns');
console.log('- Created import verification tests');

console.log('\n🎯 Next steps:');
console.log('1. Run full test suite: npm run test');
console.log('2. Check coverage: npm run test:coverage');
console.log('3. Run Cloudflare Workers tests: npm run test:cloudflare');
console.log('4. Review any remaining TypeScript errors');

console.log('\n✨ TypeScript fixes completed!');
