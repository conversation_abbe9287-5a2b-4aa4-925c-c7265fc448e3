name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '20'

jobs:
  # <PERSON><PERSON> and Type Check
  lint-and-typecheck:
    name: <PERSON><PERSON> and Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint
        run: pnpm run lint

      - name: Run TypeScript type check
        run: pnpm run type-check

  # Unit Tests with Standard Node.js (for coverage)
  test-node:
    name: Unit Tests (Node.js)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Create test environment file
        run: |
          cat > .env.test << EOF
          ENVIRONMENT=test
          WHATSAPP_VERIFY_TOKEN=test_verify_token
          WHATSAPP_ACCESS_TOKEN=test_access_token
          WHATSAPP_PHONE_NUMBER_ID=test_phone_id
          WHATSAPP_WEBHOOK_SECRET=test_webhook_secret
          DPO_COMPANY_TOKEN=test_dpo_token
          DPO_SERVICE_TYPE=test_service
          JWT_SECRET=test_jwt_secret_32_characters_long
          ENCRYPTION_KEY=test_encryption_key_32_chars_long
          EOF

      - name: Run unit tests with coverage (Node.js environment)
        run: pnpm run test:coverage
        env:
          NODE_ENV: test

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

      - name: Upload coverage artifacts
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            coverage/
            !coverage/tmp/
          retention-days: 30

  # Cloudflare Workers Tests
  test-workers:
    name: Cloudflare Workers Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Cloudflare Workers tests
        run: pnpm run test
        env:
          NODE_ENV: test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: workers-test-results
          path: |
            test-results/
            logs/
          retention-days: 7

  # Security Audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run security audit
        run: pnpm audit --audit-level moderate
        continue-on-error: true

      - name: Run dependency check
        run: pnpm run deps:check
        continue-on-error: true

  # Build and Deploy (only on main branch)
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, test-node, test-workers, security-audit]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        run: pnpm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            wrangler.toml
          retention-days: 90

      - name: Deploy to Cloudflare Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --env production

  # Coverage Report (combine results)
  coverage-report:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [test-node]
    if: needs.test-node.result == 'success'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download coverage artifacts
        uses: actions/download-artifact@v4
        with:
          name: coverage-reports
          path: coverage/
        continue-on-error: true

      - name: Generate coverage summary
        run: |
          echo "## Test Coverage Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ -f coverage/coverage-summary.json ]; then
            echo "### Coverage Summary" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "| Metric | Percentage |" >> $GITHUB_STEP_SUMMARY
            echo "|--------|------------|" >> $GITHUB_STEP_SUMMARY
            echo "| Lines | $(cat coverage/coverage-summary.json | jq -r '.total.lines.pct')% |" >> $GITHUB_STEP_SUMMARY
            echo "| Functions | $(cat coverage/coverage-summary.json | jq -r '.total.functions.pct')% |" >> $GITHUB_STEP_SUMMARY
            echo "| Branches | $(cat coverage/coverage-summary.json | jq -r '.total.branches.pct')% |" >> $GITHUB_STEP_SUMMARY
            echo "| Statements | $(cat coverage/coverage-summary.json | jq -r '.total.statements.pct')% |" >> $GITHUB_STEP_SUMMARY
          else
            echo "Coverage report not found" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            if (fs.existsSync('coverage/coverage-summary.json')) {
              const coverage = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
              const comment = `## 📊 Test Coverage Report
              
              | Metric | Percentage | Status |
              |--------|------------|--------|
              | Lines | ${coverage.total.lines.pct}% | ${coverage.total.lines.pct >= 80 ? '✅' : '❌'} |
              | Functions | ${coverage.total.functions.pct}% | ${coverage.total.functions.pct >= 80 ? '✅' : '❌'} |
              | Branches | ${coverage.total.branches.pct}% | ${coverage.total.branches.pct >= 75 ? '✅' : '❌'} |
              | Statements | ${coverage.total.statements.pct}% | ${coverage.total.statements.pct >= 80 ? '✅' : '❌'} |
              
              **Target:** 80% coverage for lines, functions, and statements; 75% for branches
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }
