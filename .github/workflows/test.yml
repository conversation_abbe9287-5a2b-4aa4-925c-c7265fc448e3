name: Cloudflare Workers Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18, 20]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Cloudflare Workers types
        run: pnpm cf-typegen

      - name: Type check
        run: pnpm type-check

      - name: Lint
        run: pnpm lint

      - name: Run Cloudflare Workers tests
        run: pnpm test:cloudflare

      - name: Run full test suite with coverage
        run: pnpm test:ci
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.node-version }}
          path: |
            test-results.xml
            coverage/
            
      - name: Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info
          delete-old-comments: true

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run security audit
        run: pnpm audit --audit-level moderate
        continue-on-error: true

      - name: Check for vulnerabilities
        run: |
          if pnpm audit --audit-level high --json 2>/dev/null | jq '.vulnerabilities | length' 2>/dev/null | grep -v '^0$' >/dev/null 2>&1; then
            echo "High severity vulnerabilities found"
            exit 1
          else
            echo "No high severity vulnerabilities found"
          fi
        continue-on-error: true

  build:
    name: Build Check
    runs-on: ubuntu-latest
    needs: [test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Install Wrangler
        run: npm install -g wrangler
        
      - name: Build project
        run: wrangler deploy --dry-run
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          
      - name: Check bundle size
        run: |
          # Check if bundle size is reasonable (under 1MB)
          if [ -f "dist/index.js" ]; then
            SIZE=$(stat -c%s "dist/index.js")
            if [ $SIZE -gt 1048576 ]; then
              echo "Bundle size too large: $SIZE bytes"
              exit 1
            fi
            echo "Bundle size: $SIZE bytes"
          fi

  coverage-check:
    name: Coverage Check
    runs-on: ubuntu-latest
    needs: [test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run coverage check
        run: |
          pnpm run test:coverage

          # Check if coverage meets minimum thresholds
          if [ -f coverage/coverage-summary.json ]; then
            COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
            echo "Line coverage: $COVERAGE%"

            # Use awk for floating point comparison instead of bc
            if awk "BEGIN {exit !($COVERAGE < 60)}"; then
              echo "Coverage below minimum threshold of 60%"
              exit 1
            fi

            echo "Coverage check passed: $COVERAGE%"
          else
            echo "Coverage report not found, skipping coverage check"
          fi

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [test]
    
    services:
      # Add any external services needed for integration tests
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run integration tests
        run: pnpm test:integration
        env:
          # Test environment variables
          NODE_ENV: test
          WHATSAPP_VERIFY_TOKEN: test_token
          DPO_COMPANY_TOKEN: test_dpo_token
          
      - name: Upload integration test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: integration-test-results
          path: test-results.xml

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, security, build, coverage-check, integration-test]
    if: always()
    
    steps:
      - name: Notify success
        if: ${{ needs.test.result == 'success' && needs.security.result == 'success' && needs.build.result == 'success' && needs.coverage-check.result == 'success' && needs.integration-test.result == 'success' }}
        run: |
          echo "✅ All checks passed successfully!"
          
      - name: Notify failure
        if: ${{ needs.test.result == 'failure' || needs.security.result == 'failure' || needs.build.result == 'failure' || needs.coverage-check.result == 'failure' || needs.integration-test.result == 'failure' }}
        run: |
          echo "❌ Some checks failed. Please review the results."
          exit 1
