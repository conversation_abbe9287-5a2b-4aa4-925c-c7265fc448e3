import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';
import path from 'path';

export default defineWorkersConfig({
  test: {
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    include: ['tests/**/*.test.ts'],
    exclude: ['node_modules', 'dist', '.wrangler'],
    poolOptions: {
      workers: {
        wrangler: {
          configPath: './wrangler.toml',
        },
        miniflare: {
          // Additional test-specific bindings
          kvNamespaces: ['TEST_KV'],
          d1Databases: {
            DB: { id: 'test-db-id' },
            TEST_DB: { id: 'test-db-id' },
          },
          // Test environment variables
          bindings: {
            ENVIRONMENT: 'test',
            WHATSAPP_VERIFY_TOKEN: 'test_verify_token',
            WHATSAPP_ACCESS_TOKEN: 'test_access_token',
            WHATSAPP_PHONE_NUMBER_ID: 'test_phone_id',
            WHATSAPP_WEBHOOK_SECRET: 'test_webhook_secret',
            DPO_COMPANY_TOKEN: 'test_dpo_token',
            DPO_SERVICE_TYPE: 'test_service',
            DPO_PAYMENT_URL: 'https://secure.3gdirectpay.com',
            DPO_PAYMENT_API: 'https://secure.3gdirectpay.com/payv2.php',
            APP_URL: 'https://test.workers.dev',
            JWT_SECRET: 'test_jwt_secret_32_characters_long',
            ENCRYPTION_KEY: 'test_encryption_key_32_chars_long',
            LOG_LEVEL: 'error',
            ENABLE_REQUEST_LOGGING: 'false',
            RATE_LIMIT_REQUESTS_PER_MINUTE: '60',
            RATE_LIMIT_BURST_SIZE: '10',
            CUSTOMER_REGISTRATION_FLOW_ID: 'test_reg_flow',
            PAYMENT_FLOW_ID: 'test_payment_flow',
            DEFAULT_CURRENCY: 'USD',
            PAYMENT_TIMEOUT_MINUTES: '15',
            SESSION_TIMEOUT_MINUTES: '30',
          },
          // Use in-memory storage for tests
          d1Persist: false,
          kvPersist: false,
          compatibilityDate: '2025-06-17',
          compatibilityFlags: ['nodejs_compat'],
        },
      },
    },
    // Disable coverage in Workers config to avoid node:inspector issues
    // Use vitest.config.ci.ts for coverage testing
    coverage: {
      enabled: false,
    },
    testTimeout: 15000,
    hookTimeout: 15000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@tests': path.resolve(__dirname, './tests'),
    },
  },
  esbuild: {
    target: 'es2022',
  },
});
