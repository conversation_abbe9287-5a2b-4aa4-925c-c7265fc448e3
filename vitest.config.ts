import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';
import path from 'path';

export default defineWorkersConfig({
  test: {
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    include: ['tests/**/*.test.ts'],
    exclude: ['node_modules', 'dist', '.wrangler'],
    poolOptions: {
      workers: {
        wrangler: {
          configPath: './wrangler.toml',
        },
        miniflare: {
          // Additional test-specific bindings
          kvNamespaces: ['TEST_KV'],
          d1Databases: {
            DB: { id: 'test-db-id' },
            TEST_DB: { id: 'test-db-id' },
          },
          // Use in-memory storage for tests
          d1Persist: false,
          kvPersist: false,
          compatibilityDate: '2025-06-17',
          compatibilityFlags: ['nodejs_compat'],
        },
      },
    },
    // Disable coverage in Workers config to avoid node:inspector issues
    // Use vitest.config.ci.ts for coverage testing
    coverage: {
      enabled: false,
    },
    testTimeout: 15000,
    hookTimeout: 15000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@tests': path.resolve(__dirname, './tests'),
    },
  },
  esbuild: {
    target: 'es2022',
  },
});
