{"version": "3.0", "screens": [{"id": "PRODUCT_SELECTION", "title": "Select Product", "terminal": false, "data": {"products": [{"id": "basic", "name": "Basic Plan", "description": "Essential features for individuals", "price": "29.99", "currency": "USD"}, {"id": "premium", "name": "Premium Plan", "description": "Advanced features for professionals", "price": "99.99", "currency": "USD"}, {"id": "enterprise", "name": "Enterprise Plan", "description": "Full features for organizations", "price": "299.99", "currency": "USD"}]}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextHeading", "text": "💳 Select Your Plan"}, {"type": "TextSubheading", "text": "Choose the plan that best fits your needs"}, {"type": "RadioButtonsGroup", "name": "selected_product", "label": "Available Plans", "required": true, "data-source": "${data.products}", "option-text-template": "${item.name} - $${item.price} ${item.currency}\n${item.description}"}, {"type": "Footer", "label": "Continue", "on-click-action": {"name": "navigate", "next": {"type": "screen", "name": "CUSTOMER_DETAILS"}, "payload": {"selected_product": "${form.selected_product}"}}}]}}, {"id": "CUSTOMER_DETAILS", "title": "Customer Details", "terminal": false, "data": {"selected_product": {}}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextHeading", "text": "📋 Customer Information"}, {"type": "TextSubheading", "text": "Please confirm your details for payment"}, {"type": "Form", "name": "customer_form", "children": [{"type": "TextInput", "name": "email", "label": "Email Address", "placeholder": "Enter your email address", "required": true, "input-type": "email"}, {"type": "TextInput", "name": "phone", "label": "Phone Number", "placeholder": "Enter your phone number", "required": true, "input-type": "phone"}, {"type": "TextInput", "name": "billing_address", "label": "Billing Address", "placeholder": "Enter your billing address", "required": false, "input-type": "text"}]}, {"type": "Footer", "label": "Continue to Payment", "on-click-action": {"name": "navigate", "next": {"type": "screen", "name": "PAYMENT_CONFIRMATION"}, "payload": {"customer_details": {"email": "${form.email}", "phone": "${form.phone}", "billing_address": "${form.billing_address}"}}}}]}}, {"id": "PAYMENT_CONFIRMATION", "title": "Payment Confirmation", "terminal": false, "data": {"selected_product": {}, "customer_details": {}}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextHeading", "text": "💰 Confirm Your Payment"}, {"type": "TextSubheading", "text": "Please review your order before proceeding"}, {"type": "TextBody", "text": "**Product:** ${data.selected_product.name}\n**Price:** $${data.selected_product.price} ${data.selected_product.currency}\n**Description:** ${data.selected_product.description}"}, {"type": "TextBody", "text": "**Customer Details:**\n**Email:** ${data.customer_details.email}\n**Phone:** ${data.customer_details.phone}"}, {"type": "Footer", "label": "Proceed to Payment", "on-click-action": {"name": "complete", "payload": {"screen": "PAYMENT_CONFIRMATION", "data": {"confirmed": true, "selected_product": "${data.selected_product}", "customer_details": "${data.customer_details}"}}}}]}}, {"id": "PAYMENT_SUCCESS", "title": "Payment Successful", "terminal": true, "data": {"payment_url": "", "payment_reference": ""}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextHeading", "text": "✅ Payment Initiated"}, {"type": "TextSubheading", "text": "Your payment has been successfully initiated"}, {"type": "TextBody", "text": "You will be redirected to complete your payment securely. Please follow the instructions in the payment gateway."}, {"type": "TextBody", "text": "**Payment Reference:** ${data.payment_reference}"}, {"type": "Footer", "label": "Complete Payment", "on-click-action": {"name": "complete", "payload": {"screen": "PAYMENT_SUCCESS", "data": {"payment_completed": true}}}}]}}]}