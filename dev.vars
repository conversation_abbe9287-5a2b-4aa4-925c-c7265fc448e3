# WhatsApp Business API Configuration
WHATSAPP_VERIFY_TOKEN=your_webhook_verify_token_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret_for_signature_verification

# DPO Pay Configuration
DPO_COMPANY_TOKEN=your_dpo_company_token_here
DPO_SERVICE_TYPE=your_dpo_service_type_here
DPO_PAYMENT_URL=https://secure.3gdirectpay.com
DPO_PAYMENT_API=https://secure.3gdirectpay.com/payv2.php

# Application Configuration
APP_URL=https://your-worker-domain.workers.dev
ENVIRONMENT=development

# Database Configuration (automatically handled by Wrangler for D1)
# DB binding is configured in wrangler.toml

# Security
JWT_SECRET=your_jwt_secret_for_session_management
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Logging and Monitoring
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# WhatsApp Flow Configuration
CUSTOMER_REGISTRATION_FLOW_ID=your_customer_registration_flow_id
PAYMENT_FLOW_ID=your_payment_flow_id

# Business Configuration
DEFAULT_CURRENCY=USD
PAYMENT_TIMEOUT_MINUTES=15
SESSION_TIMEOUT_MINUTES=30
