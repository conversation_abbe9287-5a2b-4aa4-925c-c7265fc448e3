# WhatsApp Flow Application with Cloudflare Workers

A complete, production-ready WhatsApp Flow application built with Cloudflare Workers, Hono.js, and DPO Pay integration. This application implements interactive customer registration and payment flows using WhatsApp Business API.

## 🚀 Features

### Core Functionality
- **WhatsApp Business API Integration**: Complete webhook handling and message processing
- **Interactive Flows**: Customer registration and payment flows using WhatsApp Flows
- **Payment Processing**: Full DPO Pay integration with secure payment handling
- **Clean Architecture**: Layered architecture with dependency injection
- **Database Management**: Drizzle ORM with Cloudflare D1 (SQLite)

### Security & Performance
- **Webhook Signature Verification**: Secure webhook endpoints
- **Rate Limiting**: Configurable rate limiting for API endpoints
- **Error Handling**: Comprehensive error handling and logging
- **Security Headers**: CORS, CSP, HSTS, and other security measures
- **Input Validation**: Zod-based validation for all inputs

### Production Ready
- **Monitoring**: Health checks and structured logging
- **Audit Logging**: Complete audit trail for all operations
- **Session Management**: Flow session tracking and cleanup
- **Environment Configuration**: Separate dev/prod configurations

## 🏗️ Architecture

```
src/
├── domain/           # Business entities and rules
│   └── entities/     # User, Payment, FlowSession, AuditLog
├── application/      # Use cases and business logic
│   └── services/     # UserService, PaymentService, FlowService, etc.
├── infrastructure/   # External services and data access
│   ├── adapters/     # WhatsApp API, DPO Pay adapters
│   ├── database/     # Drizzle schema and connection
│   └── repositories/ # Data access layer
├── presentation/     # Controllers and HTTP handlers
│   └── controllers/  # WhatsApp, Payment controllers
└── shared/          # Common utilities and types
    ├── container/    # Dependency injection
    ├── middleware/   # Security, error handling, rate limiting
    ├── types/        # TypeScript type definitions
    └── utils/        # Crypto, validation, logging utilities
```

## 🛠️ Technology Stack

- **Runtime**: Cloudflare Workers
- **Framework**: Hono.js v4.x
- **Database**: Drizzle ORM with Cloudflare D1 (SQLite)
- **Package Manager**: pnpm
- **Language**: TypeScript (strict mode)
- **Payment Gateway**: DPO Pay
- **Messaging**: WhatsApp Business API

## 📋 Prerequisites

1. **Cloudflare Account** with Workers and D1 access
2. **Node.js** 18+ and **pnpm**
3. **WhatsApp Business Account** with API access
4. **DPO Pay Account** for payment processing
5. **Wrangler CLI**: `npm install -g wrangler`

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd whatsapp-flows-hono

# Install dependencies
pnpm install
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# See DEPLOYMENT.md for detailed setup instructions
```

### 3. Database Setup

```bash
# Create D1 database
wrangler d1 create whatsapp-flows-db

# Run migrations
pnpm run db:migrate:local

# Seed database (optional)
wrangler d1 execute whatsapp-flows-db --local --file=./drizzle/seed.sql
```

### 4. Development

```bash
# Start development server
pnpm run dev

# Test the application
curl http://localhost:8787/
```

### 5. Deployment

```bash
# Deploy to Cloudflare Workers
pnpm run deploy

# Set environment variables (see DEPLOYMENT.md)
wrangler secret put WHATSAPP_ACCESS_TOKEN
# ... other secrets
```

## 📱 WhatsApp Flows

### Customer Registration Flow
Interactive form collecting:
- Full name
- Email address
- Phone number
- Terms agreement

### Payment Flow
Complete payment process:
1. Product selection (Basic/Premium/Enterprise plans)
2. Customer details confirmation
3. Payment initiation with DPO Pay
4. Payment completion handling

## 🔧 API Endpoints

### Webhooks
- `GET /webhook/whatsapp` - WhatsApp webhook verification
- `POST /webhook/whatsapp` - WhatsApp message/event handling
- `POST /webhook/dpo` - DPO Pay payment callbacks

### Payment Callbacks
- `GET /payment/callback` - Payment success redirect
- `GET /payment/cancel` - Payment cancellation redirect

### API Routes
- `GET /api/payment/methods` - Available payment methods
- `GET /api/payment/:id/status` - Payment status (dev only)

### Health Checks
- `GET /` - Main application health
- `GET /health` - WhatsApp service health
- `GET /health/payment` - Payment service health

## 🧪 Testing

### Run Test Suite

```bash
# Make test script executable
chmod +x tests/curl-commands.sh

# Update BASE_URL in script and run
./tests/curl-commands.sh
```

### Sample Payloads

Test payloads are available in `tests/sample-payloads/`:
- WhatsApp webhook verification
- Text message handling
- Flow response processing
- DPO payment callbacks

### Manual Testing

1. **WhatsApp Integration**:
   - Send "hello" to your WhatsApp Business number
   - Try "register" for registration flow
   - Try "pay" for payment flow

2. **Payment Testing**:
   - Complete a test payment flow
   - Verify callback handling
   - Check payment status updates

## 📊 Monitoring

### Logging
Structured JSON logging with configurable levels:
- Error tracking with context
- Request/response logging
- Audit trail for all operations

### Metrics
Monitor via Cloudflare Dashboard:
- Request volume and latency
- Error rates and types
- Database query performance
- Rate limiting effectiveness

## 🔒 Security Features

- **Webhook Signature Verification**: HMAC-SHA256 verification
- **Rate Limiting**: Configurable per-endpoint limits
- **Security Headers**: CSP, HSTS, X-Frame-Options, etc.
- **Input Validation**: Comprehensive Zod schemas
- **Error Handling**: Secure error responses
- **Audit Logging**: Complete operation tracking

## 📚 Documentation

- **[DEPLOYMENT.md](./DEPLOYMENT.md)**: Complete deployment guide
- **[API Documentation](./docs/api.md)**: Detailed API reference
- **[Flow Definitions](./flows/)**: WhatsApp Flow JSON definitions
- **[Test Payloads](./tests/sample-payloads/)**: Sample webhook payloads

## 🛠️ Development Commands

```bash
# Development
pnpm run dev              # Start dev server
pnpm run type-check       # TypeScript checking

# Database
pnpm run db:generate      # Generate migrations
pnpm run db:migrate       # Apply migrations (prod)
pnpm run db:migrate:local # Apply migrations (local)
pnpm run db:studio        # Database studio

# Deployment
pnpm run deploy           # Deploy to Cloudflare
pnpm run cf-typegen       # Generate CF types

# Quality
pnpm run lint             # ESLint
pnpm run lint:fix         # Fix linting issues
pnpm run test             # Run tests
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For deployment and configuration help, see [DEPLOYMENT.md](./DEPLOYMENT.md).

For issues and questions:
- Check the troubleshooting section in DEPLOYMENT.md
- Review Cloudflare Workers documentation
- Consult WhatsApp Business API documentation
