# WhatsApp Flow Application - Deployment Guide

## Prerequisites

1. **Cloudflare Account**: Sign up at [cloudflare.com](https://cloudflare.com)
2. **Node.js**: Version 18 or higher
3. **pnpm**: Package manager (`npm install -g pnpm`)
4. **Wrangler CLI**: Cloudflare Workers CLI (`npm install -g wrangler`)
5. **WhatsApp Business Account**: With API access
6. **DPO Pay Account**: For payment processing

## Environment Setup

### 1. <PERSON>lone and Install Dependencies

```bash
git clone <your-repo-url>
cd whatsapp-flows-hono
pnpm install
```

### 2. Configure Environment Variables

Copy the example environment file:
```bash
cp .env.example .env
```

Update `.env` with your actual values:

```env
# WhatsApp Business API Configuration
WHATSAPP_VERIFY_TOKEN=your_unique_verify_token_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret

# DPO Pay Configuration
DPO_COMPANY_TOKEN=your_dpo_company_token
DPO_SERVICE_TYPE=your_dpo_service_type
DPO_PAYMENT_URL=https://secure.3gdirectpay.com
DPO_PAYMENT_API=https://secure.3gdirectpay.com/payv2.php

# Application Configuration
APP_URL=https://your-worker-domain.workers.dev
ENVIRONMENT=production

# Security
JWT_SECRET=your_32_character_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key

# WhatsApp Flow IDs (create these in WhatsApp Business Manager)
CUSTOMER_REGISTRATION_FLOW_ID=your_registration_flow_id
PAYMENT_FLOW_ID=your_payment_flow_id
```

### 3. Authenticate with Cloudflare

```bash
wrangler login
```

## Database Setup

### 1. Create D1 Database

```bash
# Create development database
wrangler d1 create whatsapp-flows-db

# Create production database
wrangler d1 create whatsapp-flows-db-prod
```

### 2. Update wrangler.toml

Update the `database_id` values in `wrangler.toml` with the IDs from the previous step:

```toml
[[d1_databases]]
binding = "DB"
database_name = "whatsapp-flows-db"
database_id = "your-dev-database-id"

[[env.production.d1_databases]]
binding = "DB"
database_name = "whatsapp-flows-db-prod"
database_id = "your-prod-database-id"
```

### 3. Run Database Migrations

```bash
# Development
wrangler d1 migrations apply whatsapp-flows-db --local

# Production
wrangler d1 migrations apply whatsapp-flows-db-prod
```

### 4. Seed Database (Optional)

```bash
# Development
wrangler d1 execute whatsapp-flows-db --local --file=./drizzle/seed.sql

# Production
wrangler d1 execute whatsapp-flows-db-prod --file=./drizzle/seed.sql
```

## WhatsApp Flow Setup

### 1. Create Flow JSON Files

Upload the flow definitions to WhatsApp Business Manager:
- `flows/customer-registration-flow.json`
- `flows/payment-flow.json`

### 2. Get Flow IDs

After creating flows in WhatsApp Business Manager, update your environment variables with the Flow IDs.

## Deployment

### 1. Development Deployment

```bash
# Start local development server
pnpm run dev

# Test locally
curl http://localhost:8787/
```

### 2. Production Deployment

```bash
# Deploy to Cloudflare Workers
pnpm run deploy

# Verify deployment
curl https://your-worker-domain.workers.dev/
```

### 3. Set Environment Variables

```bash
# Set production environment variables
wrangler secret put WHATSAPP_ACCESS_TOKEN
wrangler secret put WHATSAPP_WEBHOOK_SECRET
wrangler secret put DPO_COMPANY_TOKEN
wrangler secret put JWT_SECRET
wrangler secret put ENCRYPTION_KEY

# Set other variables
wrangler vars put WHATSAPP_VERIFY_TOKEN "your_verify_token"
wrangler vars put WHATSAPP_PHONE_NUMBER_ID "your_phone_number_id"
wrangler vars put DPO_SERVICE_TYPE "your_service_type"
wrangler vars put APP_URL "https://your-worker-domain.workers.dev"
wrangler vars put CUSTOMER_REGISTRATION_FLOW_ID "your_flow_id"
wrangler vars put PAYMENT_FLOW_ID "your_flow_id"
```

## WhatsApp Webhook Configuration

### 1. Configure Webhook URL

In WhatsApp Business Manager:
1. Go to App Settings > Webhooks
2. Set Callback URL: `https://your-worker-domain.workers.dev/webhook/whatsapp`
3. Set Verify Token: (same as `WHATSAPP_VERIFY_TOKEN`)
4. Subscribe to: `messages`, `message_deliveries`, `message_reads`

### 2. Verify Webhook

WhatsApp will send a verification request. Check your Worker logs to ensure it's working.

## DPO Pay Configuration

### 1. Configure Webhook URLs

In your DPO Pay dashboard:
- Success URL: `https://your-worker-domain.workers.dev/payment/callback`
- Cancel URL: `https://your-worker-domain.workers.dev/payment/cancel`
- Notification URL: `https://your-worker-domain.workers.dev/webhook/dpo`

## Testing

### 1. Run Test Suite

```bash
# Make curl commands executable
chmod +x tests/curl-commands.sh

# Update BASE_URL in the script
# Then run tests
./tests/curl-commands.sh
```

### 2. Test WhatsApp Integration

1. Send "hello" to your WhatsApp Business number
2. Try the registration flow
3. Test the payment flow

### 3. Monitor Logs

```bash
# View real-time logs
wrangler tail

# View specific deployment logs
wrangler tail --env production
```

## Monitoring and Maintenance

### 1. Health Checks

- Main health: `GET /`
- Payment health: `GET /health/payment`
- WhatsApp health: `GET /health`

### 2. Database Maintenance

```bash
# Generate new migrations
pnpm run db:generate

# Apply migrations
pnpm run db:migrate

# View database studio (development)
pnpm run db:studio
```

### 3. Performance Monitoring

Monitor these metrics in Cloudflare Dashboard:
- Request volume
- Error rates
- Response times
- Database query performance

## Troubleshooting

### Common Issues

1. **Webhook Verification Fails**
   - Check `WHATSAPP_VERIFY_TOKEN` matches WhatsApp configuration
   - Verify URL is accessible

2. **Database Connection Issues**
   - Ensure D1 database is created and migrated
   - Check `wrangler.toml` database IDs

3. **Payment Failures**
   - Verify DPO Pay credentials
   - Check webhook URLs in DPO dashboard

4. **Rate Limiting Issues**
   - Adjust rate limits in environment variables
   - Monitor rate limit headers in responses

### Debug Mode

Set `LOG_LEVEL=debug` for verbose logging.

## Security Considerations

1. **Environment Variables**: Never commit secrets to version control
2. **HTTPS Only**: Ensure all webhooks use HTTPS
3. **Signature Verification**: Enable webhook signature verification
4. **Rate Limiting**: Monitor and adjust rate limits as needed
5. **Access Control**: Restrict access to sensitive endpoints

## Support

For issues and questions:
1. Check Cloudflare Workers documentation
2. Review WhatsApp Business API documentation
3. Consult DPO Pay integration guide
4. Monitor application logs for errors
