// WhatsApp Business API Types
export interface WhatsAppWebhookPayload {
  object: string;
  entry: WhatsAppEntry[];
}

export interface WhatsAppEntry {
  id: string;
  changes: WhatsAppChange[];
}

export interface WhatsAppChange {
  value: WhatsAppValue;
  field: string;
}

export interface WhatsAppValue {
  messaging_product: string;
  metadata: WhatsAppMetadata;
  contacts?: WhatsAppContact[];
  messages?: WhatsAppMessage[];
  statuses?: WhatsAppStatus[];
}

export interface WhatsAppMetadata {
  display_phone_number: string;
  phone_number_id: string;
}

export interface WhatsAppContact {
  profile: {
    name: string;
  };
  wa_id: string;
}

export interface WhatsAppMessage {
  from: string;
  id: string;
  timestamp: string;
  type: string;
  context?: {
    from: string;
    id: string;
  };
  text?: {
    body: string;
  };
  interactive?: WhatsAppInteractive;
  button?: {
    text: string;
    payload: string;
  };
}

export interface WhatsAppInteractive {
  type: string;
  nfm_reply?: {
    response_json: string;
    body: string;
    name: string;
  };
  button_reply?: {
    id: string;
    title: string;
  };
  list_reply?: {
    id: string;
    title: string;
    description?: string;
  };
}

export interface WhatsAppStatus {
  id: string;
  status: string;
  timestamp: string;
  recipient_id: string;
  conversation?: {
    id: string;
    expiration_timestamp?: string;
    origin: {
      type: string;
    };
  };
  pricing?: {
    billable: boolean;
    pricing_model: string;
    category: string;
  };
}

// WhatsApp Flow Types
export interface WhatsAppFlowRequest {
  version: string;
  action: string;
  screen: string;
  data: Record<string, any>;
  flow_token: string;
}

export interface WhatsAppFlowResponse {
  version: string;
  screen: string;
  data: Record<string, any>;
}

export interface WhatsAppFlowError {
  version: string;
  error_msg: string;
}

// Message sending types
export interface SendMessageRequest {
  messaging_product: string;
  to: string;
  type: string;
  text?: {
    body: string;
  };
  interactive?: {
    type: string;
    header?: {
      type: string;
      text: string;
    };
    body: {
      text: string;
    };
    footer?: {
      text: string;
    };
    action: {
      button?: string;
      buttons?: Array<{
        type: string;
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: Array<{
        title: string;
        rows: Array<{
          id: string;
          title: string;
          description?: string;
        }>;
      }>;
      // Flow action properties
      name?: string;
      parameters?: {
        flow_message_version?: string;
        flow_id?: string;
        flow_cta?: string;
        flow_action?: string;
        flow_action_payload?: Record<string, any>;
      };
    };
  };
}
