import type { Container } from '@/shared/container/Container';

// Hono environment types for Cloudflare Workers
export interface HonoEnv {
  Bindings: {
    DB: D1Database;
    KV: KVNamespace;
    WHATSAPP_VERIFY_TOKEN: string;
    WHATSAPP_ACCESS_TOKEN: string;
    WHATSAPP_PHONE_NUMBER_ID: string;
    WHATSAPP_WEBHOOK_SECRET: string;
    DPO_COMPANY_TOKEN: string;
    DPO_SERVICE_TYPE: string;
    DPO_PAYMENT_URL: string;
    DPO_PAYMENT_API: string;
    APP_URL: string;
    ENVIRONMENT: string;
    JWT_SECRET: string;
    ENCRYPTION_KEY: string;
    LOG_LEVEL: string;
    ENABLE_REQUEST_LOGGING: string;
    RATE_LIMIT_REQUESTS_PER_MINUTE: string;
    RATE_LIMIT_BURST_SIZE: string;
    CUSTOMER_REGISTRATION_FLOW_ID: string;
    PAYMENT_FLOW_ID: string;
    DEFAULT_CURRENCY: string;
    PAYMENT_TIMEOUT_MINUTES: string;
    SESSION_TIMEOUT_MINUTES: string;
  };
  Variables: {
    container: Container;
  };
}

// Re-export for convenience
export type { Container };
