export interface CloudflareBindings {
  DB: D1Database;
  WHATSAPP_VERIFY_TOKEN: string;
  WHATSAPP_ACCESS_TOKEN: string;
  WHATSAPP_PHONE_NUMBER_ID: string;
  WHATSAPP_WEBHOOK_SECRET: string;
  DPO_COMPANY_TOKEN: string;
  DPO_SERVICE_TYPE: string;
  DPO_PAYMENT_URL: string;
  DPO_PAYMENT_API: string;
  APP_URL: string;
  ENVIRONMENT: string;
  JWT_SECRET: string;
  ENCRYPTION_KEY: string;
  LOG_LEVEL: string;
  ENABLE_REQUEST_LOGGING: string;
  RATE_LIMIT_REQUESTS_PER_MINUTE: string;
  RATE_LIMIT_BURST_SIZE: string;
  CUSTOMER_REGISTRATION_FLOW_ID: string;
  PAYMENT_FLOW_ID: string;
  DEFAULT_CURRENCY: string;
  PAYMENT_TIMEOUT_MINUTES: string;
  SESSION_TIMEOUT_MINUTES: string;
}

export interface AppConfig {
  whatsapp: {
    verifyToken: string;
    accessToken: string;
    phoneNumberId: string;
    webhookSecret: string;
  };
  dpo: {
    companyToken: string;
    serviceType: string;
    paymentUrl: string;
    paymentApi: string;
  };
  app: {
    url: string;
    environment: string;
    jwtSecret: string;
    encryptionKey: string;
  };
  logging: {
    level: string;
    enableRequestLogging: boolean;
  };
  rateLimit: {
    requestsPerMinute: number;
    burstSize: number;
  };
  flows: {
    customerRegistrationFlowId: string;
    paymentFlowId: string;
  };
  business: {
    defaultCurrency: string;
    paymentTimeoutMinutes: number;
    sessionTimeoutMinutes: number;
  };
}
