import { z } from 'zod';

// Common validation schemas
export const phoneNumberSchema = z.string()
  .regex(/^\+?[1-9]\d{6,14}$/, 'Invalid phone number format');

export const emailSchema = z.string()
  .email('Invalid email format')
  .max(255, 'Email too long');

export const nameSchema = z.string()
  .min(1, 'Name is required')
  .max(100, 'Name too long')
  .regex(/^[a-zA-Z\s'.-]+$/, 'Name contains invalid characters');

export const amountSchema = z.number()
  .positive('Amount must be positive')
  .max(999999.99, 'Amount too large');

export const currencySchema = z.string()
  .length(3, 'Currency must be 3 characters')
  .regex(/^[A-Z]{3}$/, 'Currency must be uppercase letters');

// WhatsApp webhook validation
export const whatsappWebhookSchema = z.object({
  object: z.string(),
  entry: z.array(z.object({
    id: z.string(),
    changes: z.array(z.object({
      value: z.object({
        messaging_product: z.string(),
        metadata: z.object({
          display_phone_number: z.string(),
          phone_number_id: z.string(),
        }),
        contacts: z.array(z.object({
          profile: z.object({
            name: z.string(),
          }),
          wa_id: z.string(),
        })).optional(),
        messages: z.array(z.object({
          from: z.string(),
          id: z.string(),
          timestamp: z.string(),
          type: z.string(),
          text: z.object({
            body: z.string(),
          }).optional(),
          interactive: z.object({
            type: z.string(),
            nfm_reply: z.object({
              response_json: z.string(),
              body: z.string(),
              name: z.string(),
            }).optional(),
            button_reply: z.object({
              id: z.string(),
              title: z.string(),
            }).optional(),
          }).optional(),
        })).optional(),
      }),
      field: z.string(),
    })),
  })),
});

// Flow validation schemas
export const customerRegistrationSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  phone: phoneNumberSchema,
});

export const paymentRequestSchema = z.object({
  amount: amountSchema,
  currency: currencySchema,
  productName: z.string().min(1, 'Product name is required').max(255),
  productDescription: z.string().max(500).optional(),
  customerEmail: emailSchema,
  customerPhone: phoneNumberSchema,
});

// Utility functions
export function validatePhoneNumber(phone: string): boolean {
  return phoneNumberSchema.safeParse(phone).success;
}

export function validateEmail(email: string): boolean {
  return emailSchema.safeParse(email).success;
}

export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}
