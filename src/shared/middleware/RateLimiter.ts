import { Context, Next } from 'hono';
import type { DatabaseConnection } from '@/infrastructure/database/connection';
import { rateLimits } from '@/infrastructure/database/schema';
import { and, eq, gt, gte, lt } from 'drizzle-orm';
import { RateLimitError } from './ErrorHandler';
import type { Logger } from '@/shared/utils/logger';
import { CryptoUtils } from '@/shared/utils/crypto';

export interface RateLimitConfig {
  requestsPerMinute: number;
  burstSize: number;
  windowSizeMinutes: number;
}

export class RateLimiter {
  constructor(
    private db: DatabaseConnection,
    private logger: Logger,
    private crypto: CryptoUtils,
    private config: RateLimitConfig,
  ) {}

  async checkRateLimit(
    identifier: string,
    endpoint: string,
  ): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    const now = new Date();
    const windowStart = new Date(now.getTime() - (this.config.windowSizeMinutes * 60 * 1000));

    try {
      // Find existing rate limit record within the current window
      const existing = await this.db
        .select()
        .from(rateLimits)
        .where(
          and(
            eq(rateLimits.identifier, identifier),
            eq(rateLimits.endpoint, endpoint),
            gt(rateLimits.windowStart, windowStart.toISOString()),
          ),
        )
        .limit(1);

      let currentCount = 0;
      let recordId: string | null = null;

      if (existing.length > 0) {
        currentCount = existing[0].requestCount;
        recordId = existing[0].id;
      }

      // Check if limit exceeded
      if (currentCount >= this.config.requestsPerMinute) {
        const resetTime = new Date(windowStart.getTime() + (this.config.windowSizeMinutes * 60 * 1000));
        
        this.logger.warn('Rate limit exceeded', {
          action: 'rate_limit_check',
          identifier,
          endpoint,
          currentCount,
          limit: this.config.requestsPerMinute,
        });

        return {
          allowed: false,
          remaining: 0,
          resetTime,
        };
      }

      // Update or create rate limit record
      if (recordId) {
        await this.db
          .update(rateLimits)
          .set({
            requestCount: currentCount + 1,
            updatedAt: now.toISOString(),
          })
          .where(eq(rateLimits.id, recordId));
      } else {
        await this.db
          .insert(rateLimits)
          .values({
            id: this.crypto.generateUUID(),
            identifier,
            endpoint,
            requestCount: 1,
            windowStart: now.toISOString(),
            createdAt: now.toISOString(),
            updatedAt: now.toISOString(),
          });
      }

      const remaining = Math.max(0, this.config.requestsPerMinute - (currentCount + 1));
      const resetTime = new Date(now.getTime() + (this.config.windowSizeMinutes * 60 * 1000));

      return {
        allowed: true,
        remaining,
        resetTime,
      };
    } catch (error) {
      this.logger.error('Error checking rate limit', {
        action: 'rate_limit_check',
        identifier,
        endpoint,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Allow request on error to avoid blocking legitimate traffic
      return {
        allowed: true,
        remaining: this.config.requestsPerMinute,
        resetTime: new Date(now.getTime() + (this.config.windowSizeMinutes * 60 * 1000)),
      };
    }
  }

  createMiddleware(endpoints?: string[]) {
    return async (c: Context, next: Next) => {
      const path = new URL(c.req.url).pathname;
      
      // Skip rate limiting for certain endpoints if specified
      if (endpoints && !endpoints.some(endpoint => path.startsWith(endpoint))) {
        await next();
        return;
      }

      // Get identifier (IP address or user ID)
      const ip = c.req.header('cf-connecting-ip') || 
                 c.req.header('x-forwarded-for') || 
                 c.req.header('x-real-ip') || 
                 'unknown';

      const identifier = ip;
      const endpoint = path;

      const result = await this.checkRateLimit(identifier, endpoint);

      // Set rate limit headers
      c.header('X-RateLimit-Limit', this.config.requestsPerMinute.toString());
      c.header('X-RateLimit-Remaining', result.remaining.toString());
      c.header('X-RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000).toString());

      if (!result.allowed) {
        throw new RateLimitError('Too many requests. Please try again later.');
      }

      await next();
    };
  }

  async cleanupOldRecords(): Promise<number> {
    const cutoffTime = new Date(Date.now() - (this.config.windowSizeMinutes * 60 * 1000 * 2));

    try {
      const result = await this.db
        .delete(rateLimits)
        .where(
          gt(rateLimits.windowStart, cutoffTime.toISOString()),
        );

      this.logger.info('Cleaned up old rate limit records', {
        action: 'cleanup_rate_limits',
        deletedCount: (result as any).changes,
      });

      return (result as any).changes;
    } catch (error) {
      this.logger.error('Error cleaning up rate limit records', {
        action: 'cleanup_rate_limits',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return 0;
    }
  }
}
