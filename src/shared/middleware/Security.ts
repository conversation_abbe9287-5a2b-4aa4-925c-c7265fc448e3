import { Context, Next } from 'hono';
import type { Logger } from '@/shared/utils/logger';
import { ForbiddenError, UnauthorizedError } from './ErrorHandler';

export interface SecurityConfig {
  enableCors: boolean;
  allowedOrigins: string[];
  enableCSP: boolean;
  enableHSTS: boolean;
  maxRequestSize: number;
}

export class SecurityMiddleware {
  constructor(
    private logger: Logger,
    private config: SecurityConfig,
  ) {}

  /**
   * Content Security Policy middleware
   */
  csp() {
    return async (c: Context, next: Next) => {
      if (this.config.enableCSP) {
        c.header('Content-Security-Policy', 
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-inline'; " +
          "style-src 'self' 'unsafe-inline'; " +
          "img-src 'self' data: https:; " +
          "connect-src 'self' https:; " +
          "font-src 'self'; " +
          "object-src 'none'; " +
          "media-src 'self'; " +
          "frame-src 'none';",
        );
      }
      await next();
    };
  }

  /**
   * HTTP Strict Transport Security middleware
   */
  hsts() {
    return async (c: Context, next: Next) => {
      if (this.config.enableHSTS) {
        c.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      }
      await next();
    };
  }

  /**
   * Security headers middleware
   */
  securityHeaders() {
    return async (c: Context, next: Next) => {
      // Prevent MIME type sniffing
      c.header('X-Content-Type-Options', 'nosniff');
      
      // Prevent clickjacking
      c.header('X-Frame-Options', 'DENY');
      
      // XSS protection
      c.header('X-XSS-Protection', '1; mode=block');
      
      // Referrer policy
      c.header('Referrer-Policy', 'strict-origin-when-cross-origin');
      
      // Permissions policy
      c.header('Permissions-Policy', 
        'camera=(), microphone=(), geolocation=(), payment=()');

      await next();
    };
  }

  /**
   * Request size limiter
   */
  requestSizeLimit() {
    return async (c: Context, next: Next) => {
      const contentLength = c.req.header('content-length');
      
      if (contentLength) {
        const size = parseInt(contentLength, 10);
        if (size > this.config.maxRequestSize) {
          this.logger.warn('Request size limit exceeded', {
            action: 'request_size_check',
            size,
            limit: this.config.maxRequestSize,
            url: c.req.url,
          });
          
          return c.text('Request entity too large', 413);
        }
      }

      return await next();
    };
  }

  /**
   * IP whitelist middleware (for sensitive endpoints)
   */
  ipWhitelist(allowedIPs: string[]) {
    return async (c: Context, next: Next) => {
      const clientIP = c.req.header('cf-connecting-ip') || 
                      c.req.header('x-forwarded-for') || 
                      c.req.header('x-real-ip');

      if (!clientIP) {
        this.logger.warn('No client IP found in request', {
          action: 'ip_whitelist_check',
          url: c.req.url,
        });
        throw new ForbiddenError('Access denied');
      }

      const isAllowed = allowedIPs.some(allowedIP => {
        if (allowedIP.includes('/')) {
          // CIDR notation support would go here
          return false;
        }
        return clientIP === allowedIP;
      });

      if (!isAllowed) {
        this.logger.warn('IP not in whitelist', {
          action: 'ip_whitelist_check',
          clientIP,
          url: c.req.url,
        });
        throw new ForbiddenError('Access denied');
      }

      await next();
    };
  }

  /**
   * API key authentication middleware
   */
  apiKeyAuth(validApiKeys: string[]) {
    return async (c: Context, next: Next) => {
      const apiKey = c.req.header('x-api-key') || c.req.query('api_key');

      if (!apiKey) {
        throw new UnauthorizedError('API key required');
      }

      if (!validApiKeys.includes(apiKey)) {
        this.logger.warn('Invalid API key used', {
          action: 'api_key_auth',
          apiKey: apiKey.substring(0, 8) + '...',
          url: c.req.url,
        });
        throw new UnauthorizedError('Invalid API key');
      }

      await next();
    };
  }

  /**
   * Request logging middleware
   */
  requestLogger() {
    return async (c: Context, next: Next) => {
      const start = Date.now();
      const method = c.req.method;
      const url = c.req.url;
      const userAgent = c.req.header('user-agent');
      const ip = c.req.header('cf-connecting-ip') || 
                c.req.header('x-forwarded-for') || 
                'unknown';

      await next();

      const duration = Date.now() - start;
      const status = c.res.status;

      this.logger.info('Request processed', {
        action: 'request_log',
        method,
        url,
        status,
        duration,
        ip,
        userAgent,
      });
    };
  }

  /**
   * Webhook signature verification middleware
   */
  webhookSignatureVerification(_secret: string, headerName: string = 'x-hub-signature-256') {
    return async (c: Context, next: Next) => {
      const signature = c.req.header(headerName);
      
      if (!signature) {
        this.logger.warn('Missing webhook signature', {
          action: 'webhook_signature_verification',
          url: c.req.url,
        });
        throw new UnauthorizedError('Missing signature');
      }

      // The signature verification logic is handled in the controller
      // This middleware just ensures the header is present
      await next();
    };
  }

  /**
   * Environment-based access control
   */
  environmentAccess(allowedEnvironments: string[]) {
    return async (c: Context, next: Next) => {
      const environment = c.env?.ENVIRONMENT || 'development';
      
      if (!allowedEnvironments.includes(environment)) {
        this.logger.warn('Environment not allowed for this endpoint', {
          action: 'environment_access',
          environment,
          allowedEnvironments,
          url: c.req.url,
        });
        throw new ForbiddenError('Access denied in this environment');
      }

      await next();
    };
  }
}
