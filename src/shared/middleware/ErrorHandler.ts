import { Context, Next } from 'hono';
import type { Logger } from '@/shared/utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class ValidationError extends Error implements AppError {
  statusCode = 400;
  code = 'VALIDATION_ERROR';
  
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error implements AppError {
  statusCode = 404;
  code = 'NOT_FOUND';
  
  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error implements AppError {
  statusCode = 401;
  code = 'UNAUTHORIZED';
  
  constructor(message: string = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error implements AppError {
  statusCode = 403;
  code = 'FORBIDDEN';
  
  constructor(message: string = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends Error implements AppError {
  statusCode = 409;
  code = 'CONFLICT';
  
  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends Error implements AppError {
  statusCode = 429;
  code = 'RATE_LIMIT_EXCEEDED';
  
  constructor(message: string = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends Error implements AppError {
  statusCode = 502;
  code = 'EXTERNAL_SERVICE_ERROR';
  
  constructor(message: string, public service: string) {
    super(message);
    this.name = 'ExternalServiceError';
  }
}

export function createErrorHandler(logger: Logger) {
  return async (error: Error, c: Context): Promise<Response> => {
    const appError = error as AppError;
    const statusCode = appError.statusCode || 500;
    const code = appError.code || 'INTERNAL_SERVER_ERROR';

    // Log error with context
    const errorContext = {
      error: error.message,
      stack: error.stack,
      statusCode,
      code,
      url: c.req.url,
      method: c.req.method,
      userAgent: typeof c.req.header === 'function' ? c.req.header('user-agent') : undefined,
      ip: typeof c.req.header === 'function' ? (c.req.header('cf-connecting-ip') || c.req.header('x-forwarded-for')) : undefined,
      details: appError.details,
    };

    if (statusCode >= 500) {
      logger.error('Internal server error', errorContext);
    } else if (statusCode >= 400) {
      logger.warn('Client error', errorContext);
    }

    // Return appropriate error response
    const errorResponse = {
      error: {
        code,
        message: statusCode >= 500 && !(error instanceof ExternalServiceError)
          ? 'Internal server error. Please try again later.'
          : error.message,
        ...(appError.details && { details: appError.details }),
      },
      timestamp: new Date().toISOString(),
      path: c.req.url,
    };

    return c.json(errorResponse, statusCode as any);
  };
}

export function asyncHandler(
  fn: (c: Context, next: Next) => Promise<Response | void>,
) {
  return async (c: Context, next: Next) => {
    return await fn(c, next);
  };
}
