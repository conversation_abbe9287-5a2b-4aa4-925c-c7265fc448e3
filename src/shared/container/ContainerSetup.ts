import { Container, SERVICE_NAMES } from './Container';
export { SERVICE_NAMES };
import { createDatabaseConnection } from '@/infrastructure/database/connection';
import { UserRepository } from '@/infrastructure/repositories/UserRepository';
import { PaymentRepository } from '@/infrastructure/repositories/PaymentRepository';
import { FlowSessionRepository } from '@/infrastructure/repositories/FlowSessionRepository';
import { UserService } from '@/application/services/UserService';
import { PaymentService } from '@/application/services/PaymentService';
import { FlowService } from '@/application/services/FlowService';
import { WhatsAppService } from '@/application/services/WhatsAppService';
import { DpoService } from '@/application/services/DpoService';
import { WhatsAppAdapter } from '@/infrastructure/adapters/WhatsAppAdapter';
import { DpoAdapter } from '@/infrastructure/adapters/DpoAdapter';
import { CryptoUtils } from '@/shared/utils/crypto';
import { Logger } from '@/shared/utils/logger';
import { createAppConfig, validateConfig } from '@/shared/config/AppConfig';
import type { CloudflareBindings } from '@/shared/types/environment';

export function setupContainer(bindings: CloudflareBindings): Container {
  const container = new Container();

  // Configuration
  const config = createAppConfig(bindings);
  validateConfig(config);
  container.register(SERVICE_NAMES.CONFIG, config);

  // Utilities
  container.registerFactory(SERVICE_NAMES.CRYPTO_UTILS, () => 
    new CryptoUtils(config.app.encryptionKey),
  );

  container.registerFactory(SERVICE_NAMES.LOGGER, () => 
    new Logger(config.logging.level),
  );

  // Database
  container.registerFactory(SERVICE_NAMES.DATABASE_CONNECTION, () => 
    createDatabaseConnection(bindings),
  );

  // Repositories
  container.registerFactory(SERVICE_NAMES.USER_REPOSITORY, () => 
    new UserRepository(
      container.get(SERVICE_NAMES.DATABASE_CONNECTION),
      container.get(SERVICE_NAMES.CRYPTO_UTILS),
    ),
  );

  container.registerFactory(SERVICE_NAMES.PAYMENT_REPOSITORY, () => 
    new PaymentRepository(
      container.get(SERVICE_NAMES.DATABASE_CONNECTION),
      container.get(SERVICE_NAMES.CRYPTO_UTILS),
    ),
  );

  container.registerFactory(SERVICE_NAMES.FLOW_SESSION_REPOSITORY, () => 
    new FlowSessionRepository(
      container.get(SERVICE_NAMES.DATABASE_CONNECTION),
      container.get(SERVICE_NAMES.CRYPTO_UTILS),
    ),
  );

  // External adapters
  container.registerFactory(SERVICE_NAMES.WHATSAPP_ADAPTER, () => 
    new WhatsAppAdapter(
      config.whatsapp,
      container.get(SERVICE_NAMES.LOGGER),
    ),
  );

  container.registerFactory(SERVICE_NAMES.DPO_ADAPTER, () => 
    new DpoAdapter(
      config.dpo,
      container.get(SERVICE_NAMES.LOGGER),
    ),
  );

  // Application services
  container.registerFactory(SERVICE_NAMES.USER_SERVICE, () => 
    new UserService(
      container.get(SERVICE_NAMES.USER_REPOSITORY),
      container.get(SERVICE_NAMES.LOGGER),
    ),
  );

  container.registerFactory(SERVICE_NAMES.PAYMENT_SERVICE, () => 
    new PaymentService(
      container.get(SERVICE_NAMES.PAYMENT_REPOSITORY),
      container.get(SERVICE_NAMES.DPO_ADAPTER),
      container.get(SERVICE_NAMES.LOGGER),
      config,
    ),
  );

  container.registerFactory(SERVICE_NAMES.FLOW_SERVICE, () => 
    new FlowService(
      container.get(SERVICE_NAMES.FLOW_SESSION_REPOSITORY),
      container.get(SERVICE_NAMES.USER_SERVICE),
      container.get(SERVICE_NAMES.PAYMENT_SERVICE),
      container.get(SERVICE_NAMES.LOGGER),
      config,
    ),
  );

  container.registerFactory(SERVICE_NAMES.WHATSAPP_SERVICE, () => 
    new WhatsAppService(
      container.get(SERVICE_NAMES.WHATSAPP_ADAPTER),
      container.get(SERVICE_NAMES.FLOW_SERVICE),
      container.get(SERVICE_NAMES.USER_SERVICE),
      container.get(SERVICE_NAMES.LOGGER),
      config,
    ),
  );

  container.registerFactory(SERVICE_NAMES.DPO_SERVICE, () => 
    new DpoService(
      container.get(SERVICE_NAMES.DPO_ADAPTER),
      container.get(SERVICE_NAMES.PAYMENT_SERVICE),
      container.get(SERVICE_NAMES.LOGGER),
    ),
  );

  return container;
}
