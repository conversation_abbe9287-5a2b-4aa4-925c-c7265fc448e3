// Simple dependency injection container
export class Container {
  private services = new Map<string, any>();
  private factories = new Map<string, () => any>();

  register<T>(name: string, instance: T): void {
    this.services.set(name, instance);
  }

  registerFactory<T>(name: string, factory: () => T): void {
    this.factories.set(name, factory);
  }

  get<T>(name: string): T {
    // First check if we have a cached instance
    if (this.services.has(name)) {
      return this.services.get(name) as T;
    }

    // Then check if we have a factory
    if (this.factories.has(name)) {
      const factory = this.factories.get(name)!;
      const instance = factory();
      this.services.set(name, instance);
      return instance as T;
    }

    throw new Error(`Service '${name}' not found in container`);
  }

  has(name: string): boolean {
    return this.services.has(name) || this.factories.has(name);
  }

  clear(): void {
    this.services.clear();
    this.factories.clear();
  }
}

// Service names constants
export const SERVICE_NAMES = {
  // Database
  DATABASE_CONNECTION: 'database_connection',
  
  // Repositories
  USER_REPOSITORY: 'user_repository',
  PAYMENT_REPOSITORY: 'payment_repository',
  FLOW_SESSION_REPOSITORY: 'flow_session_repository',
  AUDIT_LOG_REPOSITORY: 'audit_log_repository',
  
  // Services
  USER_SERVICE: 'user_service',
  PAYMENT_SERVICE: 'payment_service',
  FLOW_SERVICE: 'flow_service',
  WHATSAPP_SERVICE: 'whatsapp_service',
  DPO_SERVICE: 'dpo_service',
  
  // Utils
  CRYPTO_UTILS: 'crypto_utils',
  LOGGER: 'logger',
  CONFIG: 'config',
  
  // External adapters
  WHATSAPP_ADAPTER: 'whatsapp_adapter',
  DPO_ADAPTER: 'dpo_adapter',
} as const;
