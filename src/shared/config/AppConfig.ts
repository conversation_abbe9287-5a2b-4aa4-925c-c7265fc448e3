import type { AppConfig, CloudflareBindings } from '@/shared/types/environment';

export function createAppConfig(bindings: CloudflareBindings): AppConfig {
  return {
    whatsapp: {
      verifyToken: bindings.WHATSAPP_VERIFY_TOKEN,
      accessToken: bindings.WHATSAPP_ACCESS_TOKEN,
      phoneNumberId: bindings.WHATSAPP_PHONE_NUMBER_ID,
      webhookSecret: bindings.WHATSAPP_WEBHOOK_SECRET,
    },
    dpo: {
      companyToken: bindings.DPO_COMPANY_TOKEN,
      serviceType: bindings.DPO_SERVICE_TYPE,
      paymentUrl: bindings.DPO_PAYMENT_URL,
      paymentApi: bindings.DPO_PAYMENT_API,
    },
    app: {
      url: bindings.APP_URL,
      environment: bindings.ENVIRONMENT,
      jwtSecret: bindings.JWT_SECRET,
      encryptionKey: bindings.ENCRYPTION_KEY,
    },
    logging: {
      level: bindings.LOG_LEVEL || 'info',
      enableRequestLogging: bindings.ENABLE_REQUEST_LOGGING === 'true',
    },
    rateLimit: {
      requestsPerMinute: parseInt(bindings.RATE_LIMIT_REQUESTS_PER_MINUTE || '60'),
      burstSize: parseInt(bindings.RATE_LIMIT_BURST_SIZE || '10'),
    },
    flows: {
      customerRegistrationFlowId: bindings.CUSTOMER_REGISTRATION_FLOW_ID,
      paymentFlowId: bindings.PAYMENT_FLOW_ID,
    },
    business: {
      defaultCurrency: bindings.DEFAULT_CURRENCY || 'USD',
      paymentTimeoutMinutes: parseInt(bindings.PAYMENT_TIMEOUT_MINUTES || '15'),
      sessionTimeoutMinutes: parseInt(bindings.SESSION_TIMEOUT_MINUTES || '30'),
    },
  };
}

export function validateConfig(config: AppConfig): void {
  const requiredFields = [
    'whatsapp.verifyToken',
    'whatsapp.accessToken',
    'whatsapp.phoneNumberId',
    'dpo.companyToken',
    'dpo.serviceType',
    'app.url',
    'app.jwtSecret',
    'app.encryptionKey',
  ];

  for (const field of requiredFields) {
    const value = getNestedValue(config, field);
    if (!value) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}
