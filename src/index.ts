import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { SERVICE_NAMES, setupContainer } from '@/shared/container/ContainerSetup';
import { WhatsAppController } from '@/presentation/controllers/WhatsAppController';
import { PaymentController } from '@/presentation/controllers/PaymentController';
import { createErrorHandler } from '@/shared/middleware/ErrorHandler';
import { SecurityMiddleware } from '@/shared/middleware/Security';
import { RateLimiter } from '@/shared/middleware/RateLimiter';
import type { AppConfig, CloudflareBindings } from '@/shared/types/environment';
import type { Logger } from '@/shared/utils/logger';
import type { Container } from '@/shared/container/Container';

type Env = {
  Bindings: CloudflareBindings;
  Variables: {
    container: Container;
  };
};

const app = new Hono<Env>();

// Global middleware
app.use('*', cors());
app.use('*', logger());
app.use('*', prettyJSON());

// Initialize container and store in context
app.use('*', async (c, next) => {
  const container = setupContainer(c.env);
  c.set('container', container);
  await next();
});

// Security middleware
app.use('*', async (c, next) => {
  const container = c.get('container');
  const appLogger = container.get(SERVICE_NAMES.LOGGER) as Logger;
  const config = container.get(SERVICE_NAMES.CONFIG) as AppConfig;

  const security = new SecurityMiddleware(appLogger, {
    enableCors: true,
    allowedOrigins: ['*'],
    enableCSP: true,
    enableHSTS: config.app.environment === 'production',
    maxRequestSize: 10 * 1024 * 1024, // 10MB
  });

  await security.securityHeaders()(c, next);
});

// Rate limiting for webhook endpoints
app.use('/webhook/*', async (c, next) => {
  const container = c.get('container');
  const appLogger = container.get(SERVICE_NAMES.LOGGER) as Logger;
  const config = container.get(SERVICE_NAMES.CONFIG) as AppConfig;

  const limiter = new RateLimiter(
    container.get(SERVICE_NAMES.DATABASE_CONNECTION),
    appLogger,
    container.get(SERVICE_NAMES.CRYPTO_UTILS),
    {
      requestsPerMinute: config.rateLimit.requestsPerMinute,
      burstSize: config.rateLimit.burstSize,
      windowSizeMinutes: 1,
    },
  );

  await limiter.createMiddleware(['/webhook'])(c, next);
});

// Error handling
app.onError(async (error, c) => {
  const container = c.get('container');
  if (container) {
    const appLogger = container.get(SERVICE_NAMES.LOGGER) as Logger;
    const errorHandler = createErrorHandler(appLogger);
    return await errorHandler(error, c);
  }

  return c.json({ error: 'Internal server error' }, 500);
});

// Health check
app.get('/', (c) => {
  return c.json({
    status: 'healthy',
    service: 'whatsapp-flows-hono',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// WhatsApp webhook routes
app.get('/webhook/whatsapp', async (c) => {
  const container = c.get('container');
  const controller = new WhatsAppController(
    container.get(SERVICE_NAMES.WHATSAPP_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
    container.get(SERVICE_NAMES.CRYPTO_UTILS),
  );

  return await controller.verifyWebhook(c);
});

app.post('/webhook/whatsapp', async (c) => {
  const container = c.get('container');
  const controller = new WhatsAppController(
    container.get(SERVICE_NAMES.WHATSAPP_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
    container.get(SERVICE_NAMES.CRYPTO_UTILS),
  );

  return await controller.handleWebhook(c);
});

// WhatsApp webhook info (development only)
app.get('/webhook/whatsapp/info', async (c) => {
  const container = c.get('container');
  const controller = new WhatsAppController(
    container.get(SERVICE_NAMES.WHATSAPP_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
    container.get(SERVICE_NAMES.CRYPTO_UTILS),
  );

  return await controller.getWebhookInfo(c);
});

// Payment webhook routes
app.post('/webhook/dpo', async (c) => {
  const container = c.get('container');
  const controller = new PaymentController(
    container.get(SERVICE_NAMES.DPO_SERVICE),
    container.get(SERVICE_NAMES.PAYMENT_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
  );

  return await controller.handleDpoCallback(c);
});

// Payment callback routes
app.get('/payment/callback', async (c) => {
  const container = c.get('container');
  const controller = new PaymentController(
    container.get(SERVICE_NAMES.DPO_SERVICE),
    container.get(SERVICE_NAMES.PAYMENT_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
  );

  return await controller.handlePaymentSuccess(c);
});

app.get('/payment/cancel', async (c) => {
  const container = c.get('container');
  const controller = new PaymentController(
    container.get(SERVICE_NAMES.DPO_SERVICE),
    container.get(SERVICE_NAMES.PAYMENT_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
  );

  return await controller.handlePaymentCancel(c);
});

// API routes
app.get('/api/payment/methods', async (c) => {
  const container = c.get('container');
  const controller = new PaymentController(
    container.get(SERVICE_NAMES.DPO_SERVICE),
    container.get(SERVICE_NAMES.PAYMENT_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
  );

  return await controller.getPaymentMethods(c);
});

app.get('/api/payment/:paymentId/status', async (c) => {
  const container = c.get('container');
  const controller = new PaymentController(
    container.get(SERVICE_NAMES.DPO_SERVICE),
    container.get(SERVICE_NAMES.PAYMENT_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
  );

  return await controller.getPaymentStatus(c);
});

// Health checks
app.get('/health', async (c) => {
  const container = c.get('container');
  const whatsappController = new WhatsAppController(
    container.get(SERVICE_NAMES.WHATSAPP_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
    container.get(SERVICE_NAMES.CRYPTO_UTILS),
  );

  return await whatsappController.healthCheck(c);
});

app.get('/health/payment', async (c) => {
  const container = c.get('container');
  const controller = new PaymentController(
    container.get(SERVICE_NAMES.DPO_SERVICE),
    container.get(SERVICE_NAMES.PAYMENT_SERVICE),
    container.get(SERVICE_NAMES.LOGGER),
    container.get(SERVICE_NAMES.CONFIG),
  );

  return await controller.healthCheck(c);
});

export default app;
