import type { Logger } from '@/shared/utils/logger';
import type { SendMessageRequest } from '@/shared/types/whatsapp';

export interface WhatsAppConfig {
  verifyToken: string;
  accessToken: string;
  phoneNumberId: string;
  webhookSecret: string;
}

export interface SendTextMessageRequest {
  to: string;
  text: string;
}

export interface SendInteractiveMessageRequest {
  to: string;
  type: 'button' | 'list' | 'flow';
  header?: {
    type: 'text';
    text: string;
  };
  body: {
    text: string;
  };
  footer?: {
    text: string;
  };
  action: any;
}

export interface FlowMessageRequest {
  to: string;
  flowId: string;
  flowCta: string;
  flowAction: string;
  flowActionPayload?: Record<string, any>;
  header?: {
    type: 'text';
    text: string;
  };
  body: {
    text: string;
  };
  footer?: {
    text: string;
  };
}

export class WhatsAppAdapter {
  private readonly baseUrl = 'https://graph.facebook.com/v18.0';

  constructor(
    private config: WhatsAppConfig,
    private logger: Logger,
  ) {}

  async sendTextMessage(request: SendTextMessageRequest): Promise<{ messageId: string }> {
    this.logger.info('Sending text message', {
      action: 'send_text_message',
      to: request.to,
    });

    const payload: SendMessageRequest = {
      messaging_product: 'whatsapp',
      to: request.to,
      type: 'text',
      text: {
        body: request.text,
      },
    };

    try {
      const response = await this.sendMessage(payload);
      
      this.logger.info('Text message sent successfully', {
        action: 'send_text_message',
        to: request.to,
        messageId: response.messageId,
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to send text message', {
        action: 'send_text_message',
        to: request.to,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async sendInteractiveMessage(request: SendInteractiveMessageRequest): Promise<{ messageId: string }> {
    this.logger.info('Sending interactive message', {
      action: 'send_interactive_message',
      to: request.to,
      type: request.type,
    });

    const payload: SendMessageRequest = {
      messaging_product: 'whatsapp',
      to: request.to,
      type: 'interactive',
      interactive: {
        type: request.type,
        header: request.header,
        body: request.body,
        footer: request.footer,
        action: request.action,
      },
    };

    try {
      const response = await this.sendMessage(payload);
      
      this.logger.info('Interactive message sent successfully', {
        action: 'send_interactive_message',
        to: request.to,
        type: request.type,
        messageId: response.messageId,
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to send interactive message', {
        action: 'send_interactive_message',
        to: request.to,
        type: request.type,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async sendFlowMessage(request: FlowMessageRequest): Promise<{ messageId: string }> {
    this.logger.info('Sending flow message', {
      action: 'send_flow_message',
      to: request.to,
      flowId: request.flowId,
    });

    const payload: SendMessageRequest = {
      messaging_product: 'whatsapp',
      to: request.to,
      type: 'interactive',
      interactive: {
        type: 'flow',
        header: request.header,
        body: request.body,
        footer: request.footer,
        action: {
          name: 'flow',
          parameters: {
            flow_message_version: '3',
            flow_id: request.flowId,
            flow_cta: request.flowCta,
            flow_action: request.flowAction,
            flow_action_payload: request.flowActionPayload || {},
          },
        },
      },
    };

    try {
      const response = await this.sendMessage(payload);
      
      this.logger.info('Flow message sent successfully', {
        action: 'send_flow_message',
        to: request.to,
        flowId: request.flowId,
        messageId: response.messageId,
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to send flow message', {
        action: 'send_flow_message',
        to: request.to,
        flowId: request.flowId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  private async sendMessage(payload: SendMessageRequest): Promise<{ messageId: string }> {
    const url = `${this.baseUrl}/${this.config.phoneNumberId}/messages`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`WhatsApp API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json() as any; // WhatsApp API response type
    
    if (!data.messages || !data.messages[0] || !data.messages[0].id) {
      throw new Error('Invalid response from WhatsApp API');
    }

    return {
      messageId: data.messages[0].id,
    };
  }

  async markMessageAsRead(messageId: string): Promise<void> {
    this.logger.info('Marking message as read', {
      action: 'mark_message_read',
      messageId,
    });

    const url = `${this.baseUrl}/${this.config.phoneNumberId}/messages`;

    const payload = {
      messaging_product: 'whatsapp',
      status: 'read',
      message_id: messageId,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`WhatsApp API error: ${response.status} - ${errorData}`);
      }

      this.logger.info('Message marked as read successfully', {
        action: 'mark_message_read',
        messageId,
      });
    } catch (error) {
      this.logger.error('Failed to mark message as read', {
        action: 'mark_message_read',
        messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      // Don't throw error for read receipts as it's not critical
    }
  }
}
