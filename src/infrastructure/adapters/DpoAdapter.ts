import type { Logger } from '@/shared/utils/logger';

export interface DpoConfig {
  companyToken: string;
  serviceType: string;
  paymentUrl: string;
  paymentApi: string;
}

export interface CreatePaymentTokenRequest {
  amount: number;
  currency: string;
  customerEmail: string;
  customerPhone: string;
  productName: string;
  productDescription?: string;
  transactionToken: string;
  redirectUrl: string;
  backUrl: string;
}

export interface CreatePaymentTokenResponse {
  success: boolean;
  reference: string;
  paymentUrl: string;
  transactionToken: string;
}

export interface VerifyPaymentRequest {
  transactionToken: string;
  companyRef?: string;
}

export interface VerifyPaymentResponse {
  success: boolean;
  status: string;
  amount: number;
  currency: string;
  customerEmail: string;
  customerPhone: string;
  transactionToken: string;
  reference: string;
  paymentDate?: string;
}

export class DpoAdapter {
  constructor(
    private config: DpoConfig,
    private logger: Logger,
  ) {}

  async createPaymentToken(request: CreatePaymentTokenRequest): Promise<CreatePaymentTokenResponse> {
    this.logger.info('Creating DPO payment token', {
      action: 'create_payment_token',
      amount: request.amount,
      currency: request.currency,
      transactionToken: request.transactionToken,
    });

    const payload = {
      API: '1.0',
      Request: 'createToken',
      Transaction: {
        PaymentAmount: request.amount.toString(),
        PaymentCurrency: request.currency,
        CompanyRef: request.transactionToken,
        RedirectURL: request.redirectUrl,
        BackURL: request.backUrl,
        CompanyRefUnique: '1',
        PTL: '5',
        PTLtype: 'minutes',
      },
      Services: {
        Service: {
          ServiceType: this.config.serviceType,
          ServiceDescription: request.productName,
          ServiceDate: new Date().toISOString().split('T')[0],
        },
      },
      Customer: {
        CustomerEmail: request.customerEmail,
        CustomerPhone: request.customerPhone,
      },
      CompanyToken: this.config.companyToken,
    };

    try {
      const response = await fetch(this.config.paymentApi, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`DPO API error: ${response.status}`);
      }

      const data = await response.json() as any; // DPO API response type

      if (data.Result !== '000') {
        throw new Error(`DPO API error: ${data.ResultExplanation || 'Unknown error'}`);
      }

      const paymentUrl = `${this.config.paymentUrl}/?ID=${data.TransToken}`;

      this.logger.info('DPO payment token created successfully', {
        action: 'create_payment_token',
        transactionToken: request.transactionToken,
        dpoToken: data.TransToken,
        reference: data.TransRef,
      });

      return {
        success: true,
        reference: data.TransRef,
        paymentUrl,
        transactionToken: data.TransToken,
      };
    } catch (error) {
      this.logger.error('Failed to create DPO payment token', {
        action: 'create_payment_token',
        transactionToken: request.transactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to create payment token');
    }
  }

  async verifyPayment(request: VerifyPaymentRequest): Promise<VerifyPaymentResponse> {
    this.logger.info('Verifying DPO payment', {
      action: 'verify_payment',
      transactionToken: request.transactionToken,
    });

    const payload = {
      API: '1.0',
      Request: 'verifyToken',
      TransactionToken: request.transactionToken,
      CompanyToken: this.config.companyToken,
    };

    try {
      const response = await fetch(this.config.paymentApi, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`DPO API error: ${response.status}`);
      }

      const data = await response.json() as any; // DPO API response type

      const success = data.Result === '000';
      let status = 'unknown';

      if (success) {
        // Map DPO status to our internal status
        switch (data.TransactionStatus) {
          case '1':
            status = 'completed';
            break;
          case '2':
            status = 'failed';
            break;
          case '4':
            status = 'cancelled';
            break;
          default:
            status = 'processing';
        }
      }

      this.logger.info('DPO payment verification completed', {
        action: 'verify_payment',
        transactionToken: request.transactionToken,
        success,
        status,
        amount: data.TransactionAmount,
      });

      return {
        success,
        status,
        amount: parseFloat(data.TransactionAmount || '0'),
        currency: data.TransactionCurrency || '',
        customerEmail: data.CustomerEmail || '',
        customerPhone: data.CustomerPhone || '',
        transactionToken: request.transactionToken,
        reference: data.TransactionRef || '',
        paymentDate: data.TransactionSettlementDate,
      };
    } catch (error) {
      this.logger.error('Failed to verify DPO payment', {
        action: 'verify_payment',
        transactionToken: request.transactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to verify payment');
    }
  }

  async cancelPayment(transactionToken: string): Promise<boolean> {
    this.logger.info('Cancelling DPO payment', {
      action: 'cancel_payment',
      transactionToken,
    });

    const payload = {
      API: '1.0',
      Request: 'cancelToken',
      TransactionToken: transactionToken,
      CompanyToken: this.config.companyToken,
    };

    try {
      const response = await fetch(this.config.paymentApi, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`DPO API error: ${response.status}`);
      }

      const data = await response.json() as any; // DPO API response type
      const success = data.Result === '000';

      this.logger.info('DPO payment cancellation completed', {
        action: 'cancel_payment',
        transactionToken,
        success,
      });

      return success;
    } catch (error) {
      this.logger.error('Failed to cancel DPO payment', {
        action: 'cancel_payment',
        transactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  async getPaymentMethods(): Promise<any[]> {
    this.logger.info('Getting DPO payment methods', {
      action: 'get_payment_methods',
    });

    const payload = {
      API: '1.0',
      Request: 'getPaymentMethods',
      CompanyToken: this.config.companyToken,
    };

    try {
      const response = await fetch(this.config.paymentApi, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`DPO API error: ${response.status}`);
      }

      const data = await response.json() as any; // DPO API response type

      if (data.Result !== '000') {
        throw new Error(`DPO API error: ${data.ResultExplanation || 'Unknown error'}`);
      }

      this.logger.info('DPO payment methods retrieved successfully', {
        action: 'get_payment_methods',
        methodCount: data.PaymentMethods?.length || 0,
      });

      return data.PaymentMethods || [];
    } catch (error) {
      this.logger.error('Failed to get DPO payment methods', {
        action: 'get_payment_methods',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return [];
    }
  }
}
