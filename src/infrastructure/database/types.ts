// Database types that map between Drizzle schema and domain entities
import type {
  DbAuditLog,
  DbFlowSession,
  DbNewAuditLog,
  DbNewFlowSession,
  DbNewPayment,
  DbNewRateLimit,
  DbNewUser,
  DbPayment,
  DbRateLimit,
  DbUser,
} from './schema';

import { type CreateUserRequest, User } from '@/domain/entities/User';
import { type CreatePaymentRequest, Payment } from '@/domain/entities/Payment';
import type { CreateFlowSessionRequest, FlowSession } from '@/domain/entities/FlowSession';

// Type mappers to convert between database and domain types

// User type mappings
export function mapDbUserToDomain(dbUser: DbUser): User {
  return new User({
    id: dbUser.id,
    whatsappPhoneNumber: dbUser.whatsappPhoneNumber,
    name: dbUser.name || undefined,
    email: dbUser.email || undefined,
    registrationStatus: dbUser.registrationStatus,
    createdAt: new Date(dbUser.createdAt),
    updatedAt: new Date(dbUser.updatedAt),
  });
}

export function mapDomainUserToDb(user: CreateUserRequest, id: string): DbNewUser {
  return {
    id,
    whatsappPhoneNumber: user.whatsappPhoneNumber,
    name: user.name || null,
    email: user.email || null,
    registrationStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

// Payment type mappings
export function mapDbPaymentToDomain(dbPayment: DbPayment): Payment {
  return new Payment({
    id: dbPayment.id,
    userId: dbPayment.userId,
    dpoTransactionToken: dbPayment.dpoTransactionToken,
    dpoPaymentReference: dbPayment.dpoPaymentReference || undefined,
    amount: dbPayment.amount,
    currency: dbPayment.currency,
    status: dbPayment.status,
    productName: dbPayment.productName,
    productDescription: dbPayment.productDescription || undefined,
    customerEmail: dbPayment.customerEmail,
    customerPhone: dbPayment.customerPhone,
    paymentUrl: dbPayment.paymentUrl || undefined,
    createdAt: new Date(dbPayment.createdAt),
    updatedAt: new Date(dbPayment.updatedAt),
    completedAt: dbPayment.completedAt ? new Date(dbPayment.completedAt) : undefined,
    failedAt: dbPayment.failedAt ? new Date(dbPayment.failedAt) : undefined,
    failureReason: dbPayment.failureReason || undefined,
  });
}

export function mapDomainPaymentToDb(payment: CreatePaymentRequest, id: string, dpoTransactionToken: string): DbNewPayment {
  return {
    id,
    userId: payment.userId,
    dpoTransactionToken,
    amount: payment.amount,
    currency: payment.currency,
    status: 'pending',
    productName: payment.productName,
    productDescription: payment.productDescription || null,
    customerEmail: payment.customerEmail,
    customerPhone: payment.customerPhone,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

// FlowSession type mappings
export function mapDbFlowSessionToDomain(dbSession: DbFlowSession): FlowSession {
  return {
    id: dbSession.id,
    userId: dbSession.userId,
    flowType: dbSession.flowType,
    flowId: dbSession.flowId,
    status: dbSession.status,
    currentStep: dbSession.currentStep,
    sessionData: dbSession.sessionData ? JSON.parse(dbSession.sessionData as string) : {},
    createdAt: new Date(dbSession.createdAt),
    updatedAt: new Date(dbSession.updatedAt),
    completedAt: dbSession.completedAt ? new Date(dbSession.completedAt) : undefined,
    expiresAt: new Date(dbSession.expiresAt),
  };
}

export function mapDomainFlowSessionToDb(session: CreateFlowSessionRequest, id: string): DbNewFlowSession {
  return {
    id,
    userId: session.userId,
    flowType: session.flowType,
    flowId: session.flowId,
    status: 'active',
    currentStep: session.currentStep,
    sessionData: session.sessionData ? JSON.stringify(session.sessionData) : null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    expiresAt: session.expiresAt.toISOString(),
  };
}

// Re-export database types for internal use
export type {
  DbUser,
  DbNewUser,
  DbPayment,
  DbNewPayment,
  DbFlowSession,
  DbNewFlowSession,
  DbAuditLog,
  DbNewAuditLog,
  DbRateLimit,
  DbNewRateLimit,
};

// Helper types for database operations
export type DatabaseResult<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

export type DatabaseListResult<T> = {
  success: boolean;
  data?: T[];
  total?: number;
  error?: string;
};

// Common database query options
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// Database transaction type
export type DatabaseTransaction = D1PreparedStatement[];
