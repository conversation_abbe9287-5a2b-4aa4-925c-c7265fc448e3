import { integer, real, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// Users table
export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  whatsappPhoneNumber: text('whatsapp_phone_number').notNull().unique(),
  name: text('name'),
  email: text('email'),
  registrationStatus: text('registration_status', { 
    enum: ['pending', 'completed', 'failed'], 
  }).notNull().default('pending'),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
});

// Payments table
export const payments = sqliteTable('payments', {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull().references(() => users.id),
  dpoTransactionToken: text('dpo_transaction_token').notNull(),
  dpoPaymentReference: text('dpo_payment_reference'),
  amount: real('amount').notNull(),
  currency: text('currency').notNull(),
  status: text('status', {
    enum: ['pending', 'initiated', 'processing', 'completed', 'failed', 'cancelled', 'expired'],
  }).notNull().default('pending'),
  productName: text('product_name').notNull(),
  productDescription: text('product_description'),
  customerEmail: text('customer_email').notNull(),
  customerPhone: text('customer_phone').notNull(),
  paymentUrl: text('payment_url'),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  completedAt: text('completed_at'),
  failedAt: text('failed_at'),
  failureReason: text('failure_reason'),
});

// Flow sessions table
export const flowSessions = sqliteTable('flow_sessions', {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull().references(() => users.id),
  flowType: text('flow_type', { 
    enum: ['customer_registration', 'payment'], 
  }).notNull(),
  flowId: text('flow_id').notNull(),
  status: text('status', {
    enum: ['active', 'completed', 'expired', 'cancelled', 'failed'],
  }).notNull().default('active'),
  currentStep: text('current_step').notNull(),
  sessionData: text('session_data', { mode: 'json' }),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  completedAt: text('completed_at'),
  expiresAt: text('expires_at').notNull(),
});

// Audit logs table
export const auditLogs = sqliteTable('audit_logs', {
  id: text('id').primaryKey(),
  userId: text('user_id').references(() => users.id),
  action: text('action').notNull(),
  entityType: text('entity_type').notNull(),
  entityId: text('entity_id').notNull(),
  oldValues: text('old_values', { mode: 'json' }),
  newValues: text('new_values', { mode: 'json' }),
  metadata: text('metadata', { mode: 'json' }),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
});

// Rate limiting table
export const rateLimits = sqliteTable('rate_limits', {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(), // IP address or user ID
  endpoint: text('endpoint').notNull(),
  requestCount: integer('request_count').notNull().default(0),
  windowStart: text('window_start').notNull(),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
});

// Indexes for better performance
export const userPhoneIndex = sql`CREATE INDEX IF NOT EXISTS idx_users_phone ON users(whatsapp_phone_number)`;
export const paymentUserIndex = sql`CREATE INDEX IF NOT EXISTS idx_payments_user ON payments(user_id)`;
export const paymentStatusIndex = sql`CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status)`;
export const flowSessionUserIndex = sql`CREATE INDEX IF NOT EXISTS idx_flow_sessions_user ON flow_sessions(user_id)`;
export const flowSessionStatusIndex = sql`CREATE INDEX IF NOT EXISTS idx_flow_sessions_status ON flow_sessions(status)`;
export const auditLogEntityIndex = sql`CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs(entity_type, entity_id)`;
export const rateLimitIdentifierIndex = sql`CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON rate_limits(identifier, endpoint)`;

// Export database schema types (prefixed with Db to avoid conflicts)
export type DbUser = typeof users.$inferSelect;
export type DbNewUser = typeof users.$inferInsert;
export type DbPayment = typeof payments.$inferSelect;
export type DbNewPayment = typeof payments.$inferInsert;
export type DbFlowSession = typeof flowSessions.$inferSelect;
export type DbNewFlowSession = typeof flowSessions.$inferInsert;
export type DbAuditLog = typeof auditLogs.$inferSelect;
export type DbNewAuditLog = typeof auditLogs.$inferInsert;
export type DbRateLimit = typeof rateLimits.$inferSelect;
export type DbNewRateLimit = typeof rateLimits.$inferInsert;
