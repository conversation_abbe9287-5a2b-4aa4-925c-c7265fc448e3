import { eq } from 'drizzle-orm';
import type { DatabaseConnection } from '../database/connection';
import { type DbNewUser, type DbUser, users } from '../database/schema';
import { mapDbUserToDomain, mapDomainUserToDb } from '../database/types';
import type { CreateUserRequest, UpdateUserRequest, User } from '@/domain/entities/User';
import { CryptoUtils } from '@/shared/utils/crypto';

export class UserRepository {
  constructor(
    private db: DatabaseConnection,
    private crypto: CryptoUtils,
  ) {}

  async create(request: CreateUserRequest): Promise<User> {
    const id = this.crypto.generateUUID();
    const newUser = mapDomainUserToDb(request, id);

    const result = await this.db.insert(users).values(newUser).returning();
    const dbUser = result[0] as DbUser;
    return mapDbUserToDomain(dbUser);
  }

  async findById(id: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    const dbUser = result[0] as DbUser;
    return dbUser ? mapDbUserToDomain(dbUser) : null;
  }

  async findByWhatsAppPhone(phoneNumber: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.whatsappPhoneNumber, phoneNumber))
      .limit(1);

    const dbUser = result[0] as DbUser;
    return dbUser ? mapDbUserToDomain(dbUser) : null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    const dbUser = result[0] as DbUser;
    return dbUser ? mapDbUserToDomain(dbUser) : null;
  }

  async update(id: string, request: UpdateUserRequest): Promise<User | null> {
    const now = new Date().toISOString();

    const updateData: Partial<DbNewUser> = {
      updatedAt: now,
    };

    // Only include fields that are actually provided in the request
    if (request.name !== undefined) {
      updateData.name = request.name || null;
    }
    if (request.email !== undefined) {
      updateData.email = request.email || null;
    }
    if (request.registrationStatus !== undefined) {
      updateData.registrationStatus = request.registrationStatus;
    }

    const result = await this.db
      .update(users)
      .set(updateData)
      .where(eq(users.id, id))
      .returning();

    const dbUser = result[0] as DbUser;
    return dbUser ? mapDbUserToDomain(dbUser) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.db
      .delete(users)
      .where(eq(users.id, id))
      .returning();

    // Check if any rows were returned (indicating deletion occurred)
    return result.length > 0;
  }

  async exists(phoneNumber: string): Promise<boolean> {
    const result = await this.db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.whatsappPhoneNumber, phoneNumber))
      .limit(1);

    return result.length > 0;
  }

  async getRegistrationStats(): Promise<{
    total: number;
    completed: number;
    pending: number;
    failed: number;
  }> {
    const result = await this.db
      .select({
        registrationStatus: users.registrationStatus,
      })
      .from(users);

    const stats = {
      total: result.length,
      completed: 0,
      pending: 0,
      failed: 0,
    };

    result.forEach(user => {
      stats[user.registrationStatus as keyof typeof stats]++;
    });

    return stats;
  }
}
