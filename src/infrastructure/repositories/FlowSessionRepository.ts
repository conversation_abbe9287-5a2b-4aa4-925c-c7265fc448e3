import { and, eq, lt } from 'drizzle-orm';
import type { DatabaseConnection } from '../database/connection';
import { type DbFlowSession, type DbNewFlowSession, flowSessions } from '../database/schema';
import { mapDbFlowSessionToDomain, mapDomainFlowSessionToDb } from '../database/types';
import type { CreateFlowSessionRequest, FlowSession, FlowType, UpdateFlowSessionRequest } from '@/domain/entities/FlowSession';
import { CryptoUtils } from '@/shared/utils/crypto';

export class FlowSessionRepository {
  constructor(
    private db: DatabaseConnection,
    private crypto: CryptoUtils,
  ) {}

  async create(request: CreateFlowSessionRequest): Promise<FlowSession> {
    const id = this.crypto.generateUUID();
    const newSession = mapDomainFlowSessionToDb(request, id);

    const result = await this.db.insert(flowSessions).values(newSession).returning();
    const dbSession = result[0] as DbFlowSession;
    return mapDbFlowSessionToDomain(dbSession);
  }

  async findById(id: string): Promise<FlowSession | null> {
    const result = await this.db
      .select()
      .from(flowSessions)
      .where(eq(flowSessions.id, id))
      .limit(1);

    const dbSession = result[0] as DbFlowSession;
    return dbSession ? mapDbFlowSessionToDomain(dbSession) : null;
  }

  async findActiveByUserId(userId: string): Promise<FlowSession | null> {
    const result = await this.db
      .select()
      .from(flowSessions)
      .where(
        and(
          eq(flowSessions.userId, userId),
          eq(flowSessions.status, 'active'),
        ),
      )
      .limit(1);

    const dbSession = result[0] as DbFlowSession;
    return dbSession ? mapDbFlowSessionToDomain(dbSession) : null;
  }

  async findActiveByUserIdAndType(userId: string, flowType: FlowType): Promise<FlowSession | null> {
    const result = await this.db
      .select()
      .from(flowSessions)
      .where(
        and(
          eq(flowSessions.userId, userId),
          eq(flowSessions.flowType, flowType),
          eq(flowSessions.status, 'active'),
        ),
      )
      .limit(1);

    const dbSession = result[0] as DbFlowSession;
    return dbSession ? mapDbFlowSessionToDomain(dbSession) : null;
  }

  async findByFlowId(flowId: string): Promise<FlowSession[]> {
    const result = await this.db
      .select()
      .from(flowSessions)
      .where(eq(flowSessions.flowId, flowId));

    return result.map(dbSession => mapDbFlowSessionToDomain(dbSession as DbFlowSession));
  }

  async update(id: string, request: UpdateFlowSessionRequest): Promise<FlowSession | null> {
    const now = new Date().toISOString();

    const updateData: Partial<DbNewFlowSession> = {
      status: request.status,
      currentStep: request.currentStep,
      updatedAt: now,
    };

    if (request.sessionData) {
      updateData.sessionData = JSON.stringify(request.sessionData);
    }

    if (request.status === 'completed') {
      updateData.completedAt = now;
    }

    if (request.expiresAt) {
      updateData.expiresAt = request.expiresAt.toISOString();
    }

    const result = await this.db
      .update(flowSessions)
      .set(updateData)
      .where(eq(flowSessions.id, id))
      .returning();

    const dbSession = result[0] as DbFlowSession;
    return dbSession ? mapDbFlowSessionToDomain(dbSession) : null;
  }

  async updateSessionData(id: string, sessionData: Record<string, any>): Promise<FlowSession | null> {
    const now = new Date().toISOString();

    const result = await this.db
      .update(flowSessions)
      .set({
        sessionData: JSON.stringify(sessionData),
        updatedAt: now,
      })
      .where(eq(flowSessions.id, id))
      .returning();

    const dbSession = result[0] as DbFlowSession;
    return dbSession ? mapDbFlowSessionToDomain(dbSession) : null;
  }

  async expireSession(id: string): Promise<FlowSession | null> {
    const now = new Date().toISOString();

    const result = await this.db
      .update(flowSessions)
      .set({
        status: 'expired',
        updatedAt: now,
      })
      .where(eq(flowSessions.id, id))
      .returning();

    const dbSession = result[0] as DbFlowSession;
    return dbSession ? mapDbFlowSessionToDomain(dbSession) : null;
  }

  async findExpiredSessions(): Promise<FlowSession[]> {
    const now = new Date().toISOString();

    const result = await this.db
      .select()
      .from(flowSessions)
      .where(
        and(
          eq(flowSessions.status, 'active'),
          lt(flowSessions.expiresAt, now),
        ),
      );

    return result.map(dbSession => mapDbFlowSessionToDomain(dbSession as DbFlowSession));
  }

  async cleanupExpiredSessions(): Promise<number> {
    const expiredSessions = await this.findExpiredSessions();
    
    if (expiredSessions.length === 0) {
      return 0;
    }

    const now = new Date().toISOString();

    for (const session of expiredSessions) {
      await this.db
        .update(flowSessions)
        .set({
          status: 'expired',
          updatedAt: now,
        })
        .where(eq(flowSessions.id, session.id));
    }

    return expiredSessions.length;
  }

  async getSessionStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    expired: number;
    cancelled: number;
    failed: number;
  }> {
    const result = await this.db
      .select({
        status: flowSessions.status,
      })
      .from(flowSessions);

    const stats = {
      total: result.length,
      active: 0,
      completed: 0,
      expired: 0,
      cancelled: 0,
      failed: 0,
    };

    result.forEach(session => {
      stats[session.status as keyof typeof stats]++;
    });

    return stats;
  }


}
