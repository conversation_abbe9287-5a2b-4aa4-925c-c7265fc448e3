import { and, desc, eq } from 'drizzle-orm';
import type { DatabaseConnection } from '../database/connection';
import { type DbNewPayment, type DbPayment, payments } from '../database/schema';
import { mapDbPaymentToDomain, mapDomainPaymentToDb } from '../database/types';
import type { CreatePaymentRequest, Payment, PaymentStatus, UpdatePaymentRequest } from '@/domain/entities/Payment';
import { CryptoUtils } from '@/shared/utils/crypto';

export class PaymentRepository {
  constructor(
    private db: DatabaseConnection,
    private crypto: CryptoUtils,
  ) {}

  async create(request: CreatePaymentRequest): Promise<Payment> {
    const id = this.crypto.generateUUID();
    const dpoTransactionToken = this.crypto.generateRandomString(32);
    const newPayment = mapDomainPaymentToDb(request, id, dpoTransactionToken);

    const result = await this.db.insert(payments).values(newPayment).returning();
    const dbPayment = result[0] as DbPayment;
    return mapDbPaymentToDomain(dbPayment);
  }

  async findById(id: string): Promise<Payment | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.id, id))
      .limit(1);

    const dbPayment = result[0] as DbPayment;
    return dbPayment ? mapDbPaymentToDomain(dbPayment) : null;
  }

  async findByDpoToken(dpoTransactionToken: string): Promise<Payment | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.dpoTransactionToken, dpoTransactionToken))
      .limit(1);

    const dbPayment = result[0] as DbPayment;
    return dbPayment ? mapDbPaymentToDomain(dbPayment) : null;
  }

  async findByDpoReference(dpoPaymentReference: string): Promise<Payment | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.dpoPaymentReference, dpoPaymentReference))
      .limit(1);

    const dbPayment = result[0] as DbPayment;
    return dbPayment ? mapDbPaymentToDomain(dbPayment) : null;
  }

  async findByUserId(userId: string, limit: number = 10): Promise<Payment[]> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.userId, userId))
      .orderBy(desc(payments.createdAt))
      .limit(limit);

    return result.map(dbPayment => mapDbPaymentToDomain(dbPayment as DbPayment));
  }

  async findByStatus(status: PaymentStatus, limit: number = 100): Promise<Payment[]> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.status, status))
      .orderBy(desc(payments.createdAt))
      .limit(limit);

    return result.map(dbPayment => mapDbPaymentToDomain(dbPayment as DbPayment));
  }

  async update(id: string, request: UpdatePaymentRequest): Promise<Payment | null> {
    const now = new Date().toISOString();

    const updateData: Partial<DbNewPayment> = {
      status: request.status,
      dpoPaymentReference: request.dpoPaymentReference || null,
      paymentUrl: request.paymentUrl || null,
      failureReason: request.failureReason || null,
      updatedAt: now,
    };

    // Set completion/failure timestamps based on status
    if (request.status === 'completed') {
      updateData.completedAt = now;
    } else if (request.status === 'failed') {
      updateData.failedAt = now;
    }

    const result = await this.db
      .update(payments)
      .set(updateData)
      .where(eq(payments.id, id))
      .returning();

    const dbPayment = result[0] as DbPayment;
    return dbPayment ? mapDbPaymentToDomain(dbPayment) : null;
  }

  async updateByDpoToken(dpoTransactionToken: string, request: UpdatePaymentRequest): Promise<Payment | null> {
    const now = new Date().toISOString();

    const updateData: Partial<DbNewPayment> = {
      status: request.status,
      dpoPaymentReference: request.dpoPaymentReference || null,
      paymentUrl: request.paymentUrl || null,
      failureReason: request.failureReason || null,
      updatedAt: now,
    };

    // Set completion/failure timestamps based on status
    if (request.status === 'completed') {
      updateData.completedAt = now;
    } else if (request.status === 'failed') {
      updateData.failedAt = now;
    }

    const result = await this.db
      .update(payments)
      .set(updateData)
      .where(eq(payments.dpoTransactionToken, dpoTransactionToken))
      .returning();

    const dbPayment = result[0] as DbPayment;
    return dbPayment ? mapDbPaymentToDomain(dbPayment) : null;
  }

  async getPaymentStats(): Promise<{
    total: number;
    completed: number;
    pending: number;
    failed: number;
    totalAmount: number;
    completedAmount: number;
  }> {
    const result = await this.db
      .select({
        status: payments.status,
        amount: payments.amount,
      })
      .from(payments);

    const stats = {
      total: result.length,
      completed: 0,
      pending: 0,
      failed: 0,
      totalAmount: 0,
      completedAmount: 0,
    };

    result.forEach(payment => {
      stats.totalAmount += payment.amount;
      
      if (payment.status === 'completed') {
        stats.completed++;
        stats.completedAmount += payment.amount;
      } else if (payment.status === 'pending' || payment.status === 'initiated' || payment.status === 'processing') {
        stats.pending++;
      } else if (payment.status === 'failed' || payment.status === 'cancelled' || payment.status === 'expired') {
        stats.failed++;
      }
    });

    return stats;
  }

  async findExpiredPayments(_timeoutMinutes: number = 15): Promise<Payment[]> {
    // const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000).toISOString();

    const result = await this.db
      .select()
      .from(payments)
      .where(
        and(
          eq(payments.status, 'pending'),
          // Note: In a real implementation, you'd compare with a proper timestamp
          // This is a simplified version for SQLite
        ),
      );

    return result.map(dbPayment => mapDbPaymentToDomain(dbPayment as DbPayment));
  }
}
