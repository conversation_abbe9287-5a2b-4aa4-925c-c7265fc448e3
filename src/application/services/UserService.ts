import type { UserRepository } from '@/infrastructure/repositories/UserRepository';
import type { CreateUserRequest, UpdateUserRequest, User } from '@/domain/entities/User';
import type { Logger } from '@/shared/utils/logger';
import { validateEmail, validatePhoneNumber } from '@/shared/utils/validation';

export class UserService {
  constructor(
    private userRepository: UserRepository,
    private logger: Logger,
  ) {}

  async createUser(request: CreateUserRequest): Promise<User> {
    this.logger.info('Creating new user', { 
      action: 'create_user',
      phoneNumber: request.whatsappPhoneNumber, 
    });

    // Validate input
    if (!validatePhoneNumber(request.whatsappPhoneNumber)) {
      throw new Error('Invalid phone number format');
    }

    if (request.email && !validateEmail(request.email)) {
      throw new Error('Invalid email format');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findByWhatsAppPhone(request.whatsappPhoneNumber);
    if (existingUser) {
      this.logger.warn('User already exists', { 
        action: 'create_user',
        phoneNumber: request.whatsappPhoneNumber,
        existingUserId: existingUser.id,
      });
      return existingUser;
    }

    try {
      const user = await this.userRepository.create(request);
      
      this.logger.info('User created successfully', { 
        action: 'create_user',
        userId: user.id,
        phoneNumber: user.whatsappPhoneNumber,
      });

      return user;
    } catch (error) {
      this.logger.error('Failed to create user', { 
        action: 'create_user',
        phoneNumber: request.whatsappPhoneNumber,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to create user');
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      return await this.userRepository.findById(id);
    } catch (error) {
      this.logger.error('Failed to get user by ID', { 
        action: 'get_user_by_id',
        userId: id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  async getUserByWhatsAppPhone(phoneNumber: string): Promise<User | null> {
    if (!validatePhoneNumber(phoneNumber)) {
      throw new Error('Invalid phone number format');
    }

    try {
      return await this.userRepository.findByWhatsAppPhone(phoneNumber);
    } catch (error) {
      this.logger.error('Failed to get user by phone', { 
        action: 'get_user_by_phone',
        phoneNumber,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    if (!validateEmail(email)) {
      throw new Error('Invalid email format');
    }

    try {
      return await this.userRepository.findByEmail(email);
    } catch (error) {
      this.logger.error('Failed to get user by email', { 
        action: 'get_user_by_email',
        email,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  async updateUser(id: string, request: UpdateUserRequest): Promise<User | null> {
    this.logger.info('Updating user', { 
      action: 'update_user',
      userId: id,
    });

    // Validate input
    if (request.email && !validateEmail(request.email)) {
      throw new Error('Invalid email format');
    }

    try {
      const user = await this.userRepository.update(id, request);
      
      if (user) {
        this.logger.info('User updated successfully', { 
          action: 'update_user',
          userId: id,
        });
      } else {
        this.logger.warn('User not found for update', { 
          action: 'update_user',
          userId: id,
        });
      }

      return user;
    } catch (error) {
      this.logger.error('Failed to update user', { 
        action: 'update_user',
        userId: id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to update user');
    }
  }

  async completeUserRegistration(id: string, name: string, email: string): Promise<User | null> {
    this.logger.info('Completing user registration', { 
      action: 'complete_registration',
      userId: id,
    });

    if (!validateEmail(email)) {
      throw new Error('Invalid email format');
    }

    try {
      const user = await this.userRepository.update(id, {
        name,
        email,
        registrationStatus: 'completed',
      });

      if (user) {
        this.logger.info('User registration completed', { 
          action: 'complete_registration',
          userId: id,
        });
      }

      return user;
    } catch (error) {
      this.logger.error('Failed to complete user registration', { 
        action: 'complete_registration',
        userId: id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to complete user registration');
    }
  }

  async userExists(phoneNumber: string): Promise<boolean> {
    if (!validatePhoneNumber(phoneNumber)) {
      return false;
    }

    try {
      return await this.userRepository.exists(phoneNumber);
    } catch (error) {
      this.logger.error('Failed to check if user exists', { 
        action: 'user_exists',
        phoneNumber,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  async getRegistrationStats() {
    try {
      return await this.userRepository.getRegistrationStats();
    } catch (error) {
      this.logger.error('Failed to get registration stats', { 
        action: 'get_registration_stats',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to get registration stats');
    }
  }
}
