import type { PaymentRepository } from '@/infrastructure/repositories/PaymentRepository';
import type { DpoAdapter } from '@/infrastructure/adapters/DpoAdapter';
import type { CreatePaymentRequest, Payment, PaymentStatus } from '@/domain/entities/Payment';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';
import { amountSchema, currencySchema } from '@/shared/utils/validation';

export class PaymentService {
  constructor(
    private paymentRepository: PaymentRepository,
    private dpoAdapter: DpoAdapter,
    private logger: Logger,
    private config: AppConfig,
  ) {}

  async createPayment(request: CreatePaymentRequest): Promise<Payment> {
    this.logger.info('Creating new payment', { 
      action: 'create_payment',
      userId: request.userId,
      amount: request.amount,
      currency: request.currency,
    });

    // Validate input
    const amountValidation = amountSchema.safeParse(request.amount);
    if (!amountValidation.success) {
      throw new Error('Invalid payment amount');
    }

    const currencyValidation = currencySchema.safeParse(request.currency);
    if (!currencyValidation.success) {
      throw new Error('Invalid currency');
    }

    try {
      const payment = await this.paymentRepository.create(request);
      
      this.logger.info('Payment created successfully', { 
        action: 'create_payment',
        paymentId: payment.id,
        userId: request.userId,
        amount: request.amount,
      });

      return payment;
    } catch (error) {
      this.logger.error('Failed to create payment', { 
        action: 'create_payment',
        userId: request.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to create payment');
    }
  }

  async initiatePayment(paymentId: string): Promise<{ paymentUrl: string; payment: Payment }> {
    this.logger.info('Initiating payment', { 
      action: 'initiate_payment',
      paymentId,
    });

    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error('Payment not found');
    }

    if (payment.status !== 'pending') {
      throw new Error('Payment cannot be initiated in current status');
    }

    try {
      // Create payment token with DPO
      const dpoResponse = await this.dpoAdapter.createPaymentToken({
        amount: payment.amount,
        currency: payment.currency,
        customerEmail: payment.customerEmail,
        customerPhone: payment.customerPhone,
        productName: payment.productName,
        productDescription: payment.productDescription,
        transactionToken: payment.dpoTransactionToken,
        redirectUrl: `${this.config.app.url}/payment/callback`,
        backUrl: `${this.config.app.url}/payment/cancel`,
      });

      // Update payment with DPO reference and URL
      const updatedPayment = await this.paymentRepository.update(paymentId, {
        status: 'initiated',
        dpoPaymentReference: dpoResponse.reference,
        paymentUrl: dpoResponse.paymentUrl,
      });

      if (!updatedPayment) {
        throw new Error('Failed to update payment');
      }

      this.logger.info('Payment initiated successfully', { 
        action: 'initiate_payment',
        paymentId,
        dpoReference: dpoResponse.reference,
      });

      return {
        paymentUrl: dpoResponse.paymentUrl,
        payment: updatedPayment,
      };
    } catch (error) {
      // Update payment status to failed
      await this.paymentRepository.update(paymentId, {
        status: 'failed',
        failureReason: error instanceof Error ? error.message : 'Unknown error',
      });

      this.logger.error('Failed to initiate payment with DPO', {
        action: 'initiate_payment',
        paymentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to initiate payment');
    }
  }

  async updatePaymentStatus(paymentId: string, status: PaymentStatus, failureReason?: string): Promise<Payment | null> {
    this.logger.info('Updating payment status', { 
      action: 'update_payment_status',
      paymentId,
      status,
      failureReason,
    });

    try {
      const payment = await this.paymentRepository.update(paymentId, {
        status,
        failureReason,
      });

      if (payment) {
        this.logger.info('Payment status updated successfully', {
          action: 'update_payment_status',
          paymentId,
          status,
        });
      } else {
        this.logger.warn('Payment not found for status update', {
          action: 'update_payment_status',
          paymentId,
          status,
        });
      }

      return payment;
    } catch (error) {
      this.logger.error('Failed to update payment status', { 
        action: 'update_payment_status',
        paymentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to update payment status');
    }
  }

  async handlePaymentCallback(dpoReference: string, status: string): Promise<Payment | null> {
    this.logger.info('Handling payment callback', { 
      action: 'payment_callback',
      dpoReference,
      status,
    });

    const payment = await this.paymentRepository.findByDpoReference(dpoReference);
    if (!payment) {
      this.logger.warn('Payment not found for DPO reference', { 
        action: 'payment_callback',
        dpoReference,
      });
      return null;
    }

    // Map DPO status to our payment status
    let paymentStatus: PaymentStatus;
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        paymentStatus = 'completed';
        break;
      case 'failed':
      case 'error':
        paymentStatus = 'failed';
        break;
      case 'cancelled':
        paymentStatus = 'cancelled';
        break;
      default:
        paymentStatus = 'processing';
    }

    try {
      const updatedPayment = await this.paymentRepository.update(payment.id, {
        status: paymentStatus,
      });

      this.logger.info('Payment callback processed successfully', { 
        action: 'payment_callback',
        paymentId: payment.id,
        dpoReference,
        status: paymentStatus,
      });

      return updatedPayment;
    } catch (error) {
      this.logger.error('Failed to process payment callback', { 
        action: 'payment_callback',
        paymentId: payment.id,
        dpoReference,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to process payment callback');
    }
  }

  async getPaymentById(id: string): Promise<Payment | null> {
    try {
      return await this.paymentRepository.findById(id);
    } catch (error) {
      this.logger.error('Failed to get payment by ID', { 
        action: 'get_payment_by_id',
        paymentId: id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  async getPaymentsByUserId(userId: string, limit: number = 10): Promise<Payment[]> {
    try {
      return await this.paymentRepository.findByUserId(userId, limit);
    } catch (error) {
      this.logger.error('Failed to get payments by user ID', { 
        action: 'get_payments_by_user',
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return [];
    }
  }

  async getPaymentStats() {
    try {
      return await this.paymentRepository.getPaymentStats();
    } catch (error) {
      this.logger.error('Failed to get payment stats', { 
        action: 'get_payment_stats',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to get payment stats');
    }
  }

  async getPaymentByDpoToken(dpoTransactionToken: string): Promise<Payment | null> {
    try {
      return await this.paymentRepository.findByDpoToken(dpoTransactionToken);
    } catch (error) {
      this.logger.error('Failed to get payment by DPO token', {
        action: 'get_payment_by_dpo_token',
        dpoTransactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  async processExpiredPayments(): Promise<number> {
    this.logger.info('Processing expired payments', {
      action: 'process_expired_payments',
    });

    try {
      const expiredPayments = await this.paymentRepository.findExpiredPayments(
        this.config.business.paymentTimeoutMinutes,
      );

      let processedCount = 0;
      for (const payment of expiredPayments) {
        await this.paymentRepository.update(payment.id, {
          status: 'expired',
          failureReason: 'Payment timeout exceeded',
        });
        processedCount++;
      }

      this.logger.info('Expired payments processed', {
        action: 'process_expired_payments',
        processedCount,
      });

      return processedCount;
    } catch (error) {
      this.logger.error('Failed to process expired payments', {
        action: 'process_expired_payments',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to process expired payments');
    }
  }
}
