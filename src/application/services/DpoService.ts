import type { DpoAdapter } from '@/infrastructure/adapters/DpoAdapter';
import type { PaymentService } from './PaymentService';
import type { Logger } from '@/shared/utils/logger';

export class DpoService {
  constructor(
    private dpoAdapter: DpoAdapter,
    private paymentService: PaymentService,
    private logger: Logger,
  ) {}

  async handlePaymentCallback(
    transactionToken: string,
    status: string,
    reference?: string,
  ): Promise<void> {
    this.logger.info('Handling DPO payment callback', {
      action: 'handle_payment_callback',
      transactionToken,
      status,
      reference,
    });

    try {
      // Find payment by DPO transaction token
      const payment = await this.paymentService.getPaymentByDpoToken(transactionToken);
      if (!payment) {
        this.logger.warn('Payment not found for DPO callback', {
          action: 'handle_payment_callback',
          transactionToken,
        });
        return;
      }

      // Verify payment with DPO
      const verification = await this.dpoAdapter.verifyPayment({
        transactionToken,
        companyRef: reference,
      });

      if (!verification.success) {
        this.logger.warn('DPO payment verification failed', {
          action: 'handle_payment_callback',
          transactionToken,
          paymentId: payment.id,
        });
        
        await this.paymentService.updatePaymentStatus(
          payment.id,
          'failed',
          'Payment verification failed',
        );
        return;
      }

      // Update payment status based on verification
      let paymentStatus: 'completed' | 'failed' | 'cancelled' | 'processing';
      
      switch (verification.status) {
        case 'completed':
          paymentStatus = 'completed';
          break;
        case 'failed':
          paymentStatus = 'failed';
          break;
        case 'cancelled':
          paymentStatus = 'cancelled';
          break;
        default:
          paymentStatus = 'processing';
      }

      await this.paymentService.updatePaymentStatus(payment.id, paymentStatus);

      this.logger.info('Payment callback processed successfully', {
        action: 'handle_payment_callback',
        paymentId: payment.id,
        transactionToken,
        status: paymentStatus,
      });

    } catch (error) {
      this.logger.error('Error handling payment callback', {
        action: 'handle_payment_callback',
        transactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getPaymentMethods(): Promise<any[]> {
    this.logger.info('Getting available payment methods', {
      action: 'get_payment_methods',
    });

    try {
      return await this.dpoAdapter.getPaymentMethods();
    } catch (error) {
      this.logger.error('Error getting payment methods', {
        action: 'get_payment_methods',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return [];
    }
  }

  async cancelPayment(transactionToken: string): Promise<boolean> {
    this.logger.info('Cancelling payment', {
      action: 'cancel_payment',
      transactionToken,
    });

    try {
      const result = await this.dpoAdapter.cancelPayment(transactionToken);
      
      if (result) {
        // Update payment status in our system
        const payment = await this.paymentService.getPaymentByDpoToken(transactionToken);
        if (payment) {
          await this.paymentService.updatePaymentStatus(
            payment.id,
            'cancelled',
            'Payment cancelled by user',
          );
        }
      }

      return result;
    } catch (error) {
      this.logger.error('Error cancelling payment', {
        action: 'cancel_payment',
        transactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }
}
