import type { WhatsAppAdapter } from '@/infrastructure/adapters/WhatsAppAdapter';
import type { FlowService } from './FlowService';
import type { UserService } from './UserService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';
import type { WhatsAppMessage, WhatsAppMetadata, WhatsAppStatus } from '@/shared/types/whatsapp';

export class WhatsAppService {
  constructor(
    private whatsAppAdapter: WhatsAppAdapter,
    private flowService: FlowService,
    private userService: UserService,
    private logger: Logger,
    private config: AppConfig,
  ) {}

  async handleIncomingMessage(message: WhatsAppMessage, _metadata: WhatsAppMetadata): Promise<void> {
    this.logger.info('Handling incoming WhatsApp message', {
      action: 'handle_incoming_message',
      messageId: message.id,
      from: message.from,
      type: message.type,
      timestamp: message.timestamp,
    });

    try {
      // Mark message as read
      await this.whatsAppAdapter.markMessageAsRead(message.id);

      // Get or create user
      let user = await this.userService.getUserByWhatsAppPhone(message.from);
      if (!user) {
        user = await this.userService.createUser({
          whatsappPhoneNumber: message.from,
        });
      }

      // Handle different message types
      switch (message.type) {
        case 'text':
          await this.handleTextMessage(message, user.id);
          break;
        case 'interactive':
          await this.handleInteractiveMessage(message, user.id);
          break;
        case 'button':
          await this.handleButtonMessage(message, user.id);
          break;
        default:
          this.logger.warn('Unsupported message type', {
            action: 'handle_incoming_message',
            messageId: message.id,
            type: message.type,
          });
          await this.sendUnsupportedMessageResponse(message.from);
      }
    } catch (error) {
      this.logger.error('Error handling incoming message', {
        action: 'handle_incoming_message',
        messageId: message.id,
        from: message.from,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Send error response to user
      await this.sendErrorResponse(message.from);
    }
  }

  async handleMessageStatus(status: WhatsAppStatus): Promise<void> {
    this.logger.info('Handling message status update', {
      action: 'handle_message_status',
      messageId: status.id,
      status: status.status,
      recipientId: status.recipient_id,
    });

    // Log status for monitoring purposes
    // In a production system, you might want to update message delivery status in database
  }

  private async handleTextMessage(message: WhatsAppMessage, userId: string): Promise<void> {
    if (!message.text) return;

    const text = message.text.body.toLowerCase().trim();

    this.logger.info('Processing text message', {
      action: 'handle_text_message',
      userId,
      messageId: message.id,
      textLength: text.length,
    });

    // Check for active flow session
    const activeSession = await this.flowService.getActiveSession(userId);
    if (activeSession) {
      // Continue existing flow
      await this.flowService.processFlowInput(activeSession.id, text);
      return;
    }

    // Handle commands
    switch (text) {
      case 'hi':
      case 'hello':
      case 'start':
        await this.sendWelcomeMessage(message.from);
        break;
      case 'register':
        await this.startRegistrationFlow(message.from, userId);
        break;
      case 'pay':
      case 'payment':
        await this.startPaymentFlow(message.from, userId);
        break;
      case 'help':
        await this.sendHelpMessage(message.from);
        break;
      case 'status':
        await this.sendUserStatus(message.from, userId);
        break;
      default:
        await this.sendUnknownCommandResponse(message.from);
    }
  }

  private async handleInteractiveMessage(message: WhatsAppMessage, userId: string): Promise<void> {
    if (!message.interactive) return;

    this.logger.info('Processing interactive message', {
      action: 'handle_interactive_message',
      userId,
      messageId: message.id,
      interactiveType: message.interactive.type,
    });

    // Handle flow responses
    if (message.interactive.nfm_reply) {
      const flowResponse = JSON.parse(message.interactive.nfm_reply.response_json);
      await this.flowService.processFlowResponse(userId, flowResponse);
      return;
    }

    // Handle button replies
    if (message.interactive.button_reply) {
      const buttonId = message.interactive.button_reply.id;
      await this.handleButtonAction(message.from, userId, buttonId);
      return;
    }

    // Handle list replies
    if (message.interactive.list_reply) {
      const listId = message.interactive.list_reply.id;
      await this.handleListAction(message.from, userId, listId);
      return;
    }
  }

  private async handleButtonMessage(message: WhatsAppMessage, userId: string): Promise<void> {
    if (!message.button) return;

    this.logger.info('Processing button message', {
      action: 'handle_button_message',
      userId,
      messageId: message.id,
      payload: message.button.payload,
    });

    await this.handleButtonAction(message.from, userId, message.button.payload);
  }

  private async handleButtonAction(phoneNumber: string, userId: string, buttonId: string): Promise<void> {
    switch (buttonId) {
      case 'start_registration':
        await this.startRegistrationFlow(phoneNumber, userId);
        break;
      case 'start_payment':
        await this.startPaymentFlow(phoneNumber, userId);
        break;
      case 'view_status':
        await this.sendUserStatus(phoneNumber, userId);
        break;
      case 'get_help':
        await this.sendHelpMessage(phoneNumber);
        break;
      default:
        await this.sendUnknownCommandResponse(phoneNumber);
    }
  }

  private async handleListAction(_phoneNumber: string, userId: string, listId: string): Promise<void> {
    // Handle list selections (e.g., product selection in payment flow)
    if (listId.startsWith('product_')) {
      const productId = listId.replace('product_', '');
      await this.flowService.selectProduct(userId, productId);
    }
  }

  private async sendWelcomeMessage(phoneNumber: string): Promise<void> {
    await this.whatsAppAdapter.sendInteractiveMessage({
      to: phoneNumber,
      type: 'button',
      body: {
        text: '👋 Welcome to our WhatsApp service!\n\nI can help you with:\n• Customer registration\n• Making payments\n• Checking your status\n\nWhat would you like to do?',
      },
      action: {
        buttons: [
          {
            type: 'reply',
            reply: {
              id: 'start_registration',
              title: '📝 Register',
            },
          },
          {
            type: 'reply',
            reply: {
              id: 'start_payment',
              title: '💳 Make Payment',
            },
          },
          {
            type: 'reply',
            reply: {
              id: 'get_help',
              title: '❓ Help',
            },
          },
        ],
      },
    });
  }

  private async startRegistrationFlow(phoneNumber: string, userId: string): Promise<void> {
    try {
      await this.flowService.startCustomerRegistrationFlow(userId);
      
      await this.whatsAppAdapter.sendFlowMessage({
        to: phoneNumber,
        flowId: this.config.flows.customerRegistrationFlowId,
        flowCta: 'Start Registration',
        flowAction: 'navigate',
        flowActionPayload: {
          screen: 'registration_form',
        },
        body: {
          text: '📝 Let\'s get you registered! Please fill out the form below.',
        },
      });
    } catch (error) {
      this.logger.error('Failed to start registration flow', {
        action: 'start_registration_flow',
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      await this.sendErrorResponse(phoneNumber);
    }
  }

  private async startPaymentFlow(phoneNumber: string, userId: string): Promise<void> {
    try {
      // Check if user is registered
      const user = await this.userService.getUserById(userId);
      if (!user || user.registrationStatus !== 'completed') {
        await this.whatsAppAdapter.sendTextMessage({
          to: phoneNumber,
          text: '❌ You need to complete registration before making payments. Please register first.',
        });
        return;
      }

      await this.flowService.startPaymentFlow(userId);
      
      await this.whatsAppAdapter.sendFlowMessage({
        to: phoneNumber,
        flowId: this.config.flows.paymentFlowId,
        flowCta: 'Make Payment',
        flowAction: 'navigate',
        flowActionPayload: {
          screen: 'product_selection',
        },
        body: {
          text: '💳 Ready to make a payment? Select your product and proceed.',
        },
      });
    } catch (error) {
      this.logger.error('Failed to start payment flow', {
        action: 'start_payment_flow',
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      await this.sendErrorResponse(phoneNumber);
    }
  }

  private async sendHelpMessage(phoneNumber: string): Promise<void> {
    await this.whatsAppAdapter.sendTextMessage({
      to: phoneNumber,
      text: `❓ *Help & Commands*

*Available Commands:*
• "register" - Start customer registration
• "pay" or "payment" - Make a payment
• "status" - Check your registration status
• "help" - Show this help message

*Getting Started:*
1. First, complete your registration
2. Then you can make payments
3. Use the interactive buttons for easier navigation

Need more help? Contact our support team.`,
    });
  }

  private async sendUserStatus(phoneNumber: string, userId: string): Promise<void> {
    try {
      const user = await this.userService.getUserById(userId);
      if (!user) {
        await this.sendErrorResponse(phoneNumber);
        return;
      }

      const statusEmoji = user.registrationStatus === 'completed' ? '✅' : 
                         user.registrationStatus === 'pending' ? '⏳' : '❌';

      await this.whatsAppAdapter.sendTextMessage({
        to: phoneNumber,
        text: `📊 *Your Status*

${statusEmoji} Registration: ${user.registrationStatus}
📱 Phone: ${user.whatsappPhoneNumber}
👤 Name: ${user.name || 'Not provided'}
📧 Email: ${user.email || 'Not provided'}

*Last Updated:* ${new Date(user.updatedAt).toLocaleDateString()}`,
      });
    } catch (error) {
      this.logger.error('Failed to send user status', {
        action: 'send_user_status',
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      await this.sendErrorResponse(phoneNumber);
    }
  }

  private async sendUnknownCommandResponse(phoneNumber: string): Promise<void> {
    await this.whatsAppAdapter.sendTextMessage({
      to: phoneNumber,
      text: '❓ I didn\'t understand that command. Type "help" to see available commands or use the menu buttons.',
    });
  }

  private async sendUnsupportedMessageResponse(phoneNumber: string): Promise<void> {
    await this.whatsAppAdapter.sendTextMessage({
      to: phoneNumber,
      text: '⚠️ Sorry, I can only process text messages and interactive responses. Please send a text message or use the buttons.',
    });
  }

  private async sendErrorResponse(phoneNumber: string): Promise<void> {
    await this.whatsAppAdapter.sendTextMessage({
      to: phoneNumber,
      text: '❌ Sorry, something went wrong. Please try again later or contact support if the problem persists.',
    });
  }
}
