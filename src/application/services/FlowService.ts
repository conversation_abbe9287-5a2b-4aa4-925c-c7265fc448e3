import type { FlowSessionRepository } from '@/infrastructure/repositories/FlowSessionRepository';
import type { UserService } from './UserService';
import type { PaymentService } from './PaymentService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';
import type { FlowSession } from '@/domain/entities/FlowSession';
import { customerRegistrationSchema } from '@/shared/utils/validation';

export interface FlowResponse {
  screen: string;
  data: Record<string, any>;
  version: string;
}

export interface ProductInfo {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
}

export class FlowService {
  private readonly products: ProductInfo[] = [
    {
      id: 'basic',
      name: 'Basic Plan',
      description: 'Essential features for individuals',
      price: 29.99,
      currency: 'USD',
    },
    {
      id: 'premium',
      name: 'Premium Plan',
      description: 'Advanced features for professionals',
      price: 99.99,
      currency: 'USD',
    },
    {
      id: 'enterprise',
      name: 'Enterprise Plan',
      description: 'Full features for organizations',
      price: 299.99,
      currency: 'USD',
    },
  ];

  constructor(
    private flowSessionRepository: FlowSessionRepository,
    private userService: UserService,
    private paymentService: PaymentService,
    private logger: Logger,
    private config: AppConfig,
  ) {}

  async startCustomerRegistrationFlow(userId: string): Promise<FlowSession> {
    this.logger.info('Starting customer registration flow', {
      action: 'start_registration_flow',
      userId,
    });

    // Check for existing active session
    const existingSession = await this.flowSessionRepository.findActiveByUserIdAndType(
      userId, 
      'customer_registration',
    );

    if (existingSession) {
      this.logger.info('Existing registration session found', {
        action: 'start_registration_flow',
        userId,
        sessionId: existingSession.id,
      });
      return existingSession;
    }

    // Create new session
    const expiresAt = new Date(Date.now() + this.config.business.sessionTimeoutMinutes * 60 * 1000);
    
    const session = await this.flowSessionRepository.create({
      userId,
      flowType: 'customer_registration',
      flowId: this.config.flows.customerRegistrationFlowId,
      currentStep: 'start',
      sessionData: {},
      expiresAt,
    });

    this.logger.info('Customer registration flow started', {
      action: 'start_registration_flow',
      userId,
      sessionId: session.id,
    });

    return session;
  }

  async startPaymentFlow(userId: string): Promise<FlowSession> {
    this.logger.info('Starting payment flow', {
      action: 'start_payment_flow',
      userId,
    });

    // Check for existing active session
    const existingSession = await this.flowSessionRepository.findActiveByUserIdAndType(
      userId, 
      'payment',
    );

    if (existingSession) {
      this.logger.info('Existing payment session found', {
        action: 'start_payment_flow',
        userId,
        sessionId: existingSession.id,
      });
      return existingSession;
    }

    // Create new session with product list
    const expiresAt = new Date(Date.now() + this.config.business.sessionTimeoutMinutes * 60 * 1000);
    
    const session = await this.flowSessionRepository.create({
      userId,
      flowType: 'payment',
      flowId: this.config.flows.paymentFlowId,
      currentStep: 'product_selection',
      sessionData: {
        products: this.products,
      },
      expiresAt,
    });

    this.logger.info('Payment flow started', {
      action: 'start_payment_flow',
      userId,
      sessionId: session.id,
    });

    return session;
  }

  async processFlowResponse(userId: string, flowResponse: FlowResponse): Promise<void> {
    this.logger.info('Processing flow response', {
      action: 'process_flow_response',
      userId,
      screen: flowResponse.screen,
      version: flowResponse.version,
    });

    const activeSession = await this.flowSessionRepository.findActiveByUserId(userId);
    if (!activeSession) {
      this.logger.warn('No active session found for flow response', {
        action: 'process_flow_response',
        userId,
      });
      return;
    }

    try {
      switch (activeSession.flowType) {
        case 'customer_registration':
          await this.processRegistrationFlowResponse(activeSession, flowResponse);
          break;
        case 'payment':
          await this.processPaymentFlowResponse(activeSession, flowResponse);
          break;
        default:
          this.logger.warn('Unknown flow type', {
            action: 'process_flow_response',
            flowType: activeSession.flowType,
            sessionId: activeSession.id,
          });
      }
    } catch (error) {
      this.logger.error('Error processing flow response', {
        action: 'process_flow_response',
        userId,
        sessionId: activeSession.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Mark session as failed
      await this.flowSessionRepository.update(activeSession.id, {
        status: 'failed',
      });
    }
  }

  private async processRegistrationFlowResponse(session: FlowSession, response: FlowResponse): Promise<void> {
    this.logger.info('Processing registration flow response', {
      action: 'process_registration_response',
      sessionId: session.id,
      screen: response.screen,
    });

    switch (response.screen) {
      case 'registration_form':
        await this.handleRegistrationFormSubmission(session, response.data);
        break;
      case 'confirmation':
        await this.handleRegistrationConfirmation(session, response.data);
        break;
      default:
        this.logger.warn('Unknown registration screen', {
          action: 'process_registration_response',
          screen: response.screen,
          sessionId: session.id,
        });
    }
  }

  private async processPaymentFlowResponse(session: FlowSession, response: FlowResponse): Promise<void> {
    this.logger.info('Processing payment flow response', {
      action: 'process_payment_response',
      sessionId: session.id,
      screen: response.screen,
    });

    switch (response.screen) {
      case 'product_selection':
        await this.handleProductSelection(session, response.data);
        break;
      case 'customer_details':
        await this.handleCustomerDetails(session, response.data);
        break;
      case 'payment_confirmation':
        await this.handlePaymentConfirmation(session, response.data);
        break;
      default:
        this.logger.warn('Unknown payment screen', {
          action: 'process_payment_response',
          screen: response.screen,
          sessionId: session.id,
        });
    }
  }

  private async handleRegistrationFormSubmission(session: FlowSession, data: Record<string, any>): Promise<void> {
    // Validate registration data
    const validation = customerRegistrationSchema.safeParse(data);
    if (!validation.success) {
      throw new Error('Invalid registration data');
    }

    const { name, email, phone } = validation.data;

    // Update user with registration data
    await this.userService.completeUserRegistration(session.userId, name, email);

    // Update session
    await this.flowSessionRepository.update(session.id, {
      currentStep: 'completed',
      status: 'completed',
      sessionData: {
        ...session.sessionData,
        registrationData: { name, email, phone },
      },
    });

    this.logger.info('Registration completed successfully', {
      action: 'handle_registration_form',
      sessionId: session.id,
      userId: session.userId,
    });
  }

  private async handleRegistrationConfirmation(session: FlowSession, _data: Record<string, any>): Promise<void> {
    // Mark session as completed
    await this.flowSessionRepository.update(session.id, {
      status: 'completed',
    });

    this.logger.info('Registration confirmation received', {
      action: 'handle_registration_confirmation',
      sessionId: session.id,
    });
  }

  private async handleProductSelection(session: FlowSession, data: Record<string, any>): Promise<void> {
    const productId = data.selectedProduct;
    const product = this.products.find(p => p.id === productId);

    if (!product) {
      throw new Error('Invalid product selection');
    }

    // Update session with selected product
    await this.flowSessionRepository.update(session.id, {
      currentStep: 'customer_details',
      sessionData: {
        ...session.sessionData,
        selectedProduct: product,
      },
    });

    this.logger.info('Product selected', {
      action: 'handle_product_selection',
      sessionId: session.id,
      productId,
      productName: product.name,
    });
  }

  private async handleCustomerDetails(session: FlowSession, data: Record<string, any>): Promise<void> {
    // Update session with customer details
    await this.flowSessionRepository.update(session.id, {
      currentStep: 'payment_confirmation',
      sessionData: {
        ...session.sessionData,
        customerDetails: data,
      },
    });

    this.logger.info('Customer details collected', {
      action: 'handle_customer_details',
      sessionId: session.id,
    });
  }

  private async handlePaymentConfirmation(session: FlowSession, _data: Record<string, any>): Promise<void> {
    const sessionData = session.sessionData;
    const product = sessionData.selectedProduct;
    const customerDetails = sessionData.customerDetails;

    if (!product || !customerDetails) {
      throw new Error('Missing payment data');
    }

    // Create payment
    const payment = await this.paymentService.createPayment({
      userId: session.userId,
      amount: product.price,
      currency: product.currency,
      productName: product.name,
      productDescription: product.description,
      customerEmail: customerDetails.email,
      customerPhone: customerDetails.phone,
    });

    // Initiate payment with DPO
    const { paymentUrl } = await this.paymentService.initiatePayment(payment.id);

    // Update session with payment info
    await this.flowSessionRepository.update(session.id, {
      status: 'completed',
      sessionData: {
        ...sessionData,
        paymentId: payment.id,
        paymentUrl,
      },
    });

    this.logger.info('Payment initiated successfully', {
      action: 'handle_payment_confirmation',
      sessionId: session.id,
      paymentId: payment.id,
    });
  }

  async processFlowInput(sessionId: string, input: string): Promise<void> {
    this.logger.info('Processing flow input', {
      action: 'process_flow_input',
      sessionId,
      inputLength: input.length,
    });

    const session = await this.flowSessionRepository.findById(sessionId);
    if (!session || session.status !== 'active') {
      this.logger.warn('Invalid or inactive session for flow input', {
        action: 'process_flow_input',
        sessionId,
      });
      return;
    }

    // Handle text input based on current step
    // This is a simplified implementation - in practice, you'd have more sophisticated flow logic
    switch (session.currentStep) {
      case 'awaiting_confirmation':
        if (input.toLowerCase().includes('yes') || input.toLowerCase().includes('confirm')) {
          await this.flowSessionRepository.update(sessionId, {
            status: 'completed',
          });
        } else {
          await this.flowSessionRepository.update(sessionId, {
            status: 'cancelled',
          });
        }
        break;
      default:
        this.logger.info('No specific handler for flow input', {
          action: 'process_flow_input',
          sessionId,
          currentStep: session.currentStep,
        });
    }
  }

  async selectProduct(userId: string, productId: string): Promise<void> {
    const session = await this.flowSessionRepository.findActiveByUserIdAndType(userId, 'payment');
    if (!session) {
      throw new Error('No active payment session');
    }

    const product = this.products.find(p => p.id === productId);
    if (!product) {
      throw new Error('Invalid product');
    }

    await this.flowSessionRepository.update(session.id, {
      currentStep: 'customer_details',
      sessionData: {
        ...session.sessionData,
        selectedProduct: product,
      },
    });
  }

  async getActiveSession(userId: string): Promise<FlowSession | null> {
    return await this.flowSessionRepository.findActiveByUserId(userId);
  }

  async expireOldSessions(): Promise<number> {
    this.logger.info('Expiring old sessions', {
      action: 'expire_old_sessions',
    });

    return await this.flowSessionRepository.cleanupExpiredSessions();
  }
}
