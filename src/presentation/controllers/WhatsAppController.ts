import { Context } from 'hono';
import type { WhatsAppService } from '@/application/services/WhatsAppService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';
import { CryptoUtils } from '@/shared/utils/crypto';
import { whatsappWebhookSchema } from '@/shared/utils/validation';

export class WhatsAppController {
  constructor(
    private whatsAppService: WhatsAppService,
    private logger: Logger,
    private config: AppConfig,
    private crypto: CryptoUtils,
  ) {}

  /**
   * Handle WhatsApp webhook verification (GET request)
   */
  async verifyWebhook(c: Context): Promise<Response> {
    this.logger.info('WhatsApp webhook verification request', {
      action: 'webhook_verification',
      url: c.req.url,
    });

    try {
      const mode = c.req.query('hub.mode');
      const token = c.req.query('hub.verify_token');
      const challenge = c.req.query('hub.challenge');

      this.logger.debug('Webhook verification parameters', {
        action: 'webhook_verification',
        mode,
        token: token ? '***' : undefined,
        challenge: challenge ? '***' : undefined,
      });

      // Verify the mode and token
      if (mode === 'subscribe' && token === this.config.whatsapp.verifyToken) {
        this.logger.info('Webhook verification successful', {
          action: 'webhook_verification',
          mode,
          challenge: challenge ? 'present' : 'missing',
        });

        // Return the challenge to complete verification
        return c.text(challenge || '', 200);
      } else {
        this.logger.warn('Webhook verification failed', {
          action: 'webhook_verification',
          mode,
          tokenMatch: token === this.config.whatsapp.verifyToken,
          expectedMode: 'subscribe',
        });

        return c.text('Forbidden', 403);
      }
    } catch (error) {
      this.logger.error('Error during webhook verification', {
        action: 'webhook_verification',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.text('Internal Server Error', 500);
    }
  }

  /**
   * Handle WhatsApp webhook events (POST request)
   */
  async handleWebhook(c: Context): Promise<Response> {
    this.logger.info('WhatsApp webhook event received', {
      action: 'webhook_event',
      method: c.req.method,
      contentType: c.req.header('content-type'),
    });

    try {
      // Verify webhook signature if secret is configured
      if (this.config.whatsapp.webhookSecret) {
        const signature = c.req.header('x-hub-signature-256');
        if (!signature) {
          this.logger.warn('Missing webhook signature', {
            action: 'webhook_event',
          });
          return c.text('Unauthorized', 401);
        }

        const body = await c.req.text();
        // const expectedSignature = 'sha256=' + this.crypto.createHmacSignature(
        //   body,
        //   this.config.whatsapp.webhookSecret,
        // );

        if (!this.crypto.verifyHmacSignature(body, signature.replace('sha256=', ''), this.config.whatsapp.webhookSecret)) {
          this.logger.warn('Invalid webhook signature', {
            action: 'webhook_event',
            signature: signature ? 'present' : 'missing',
          });
          return c.text('Unauthorized', 401);
        }

        // Parse the verified body
        const payload = JSON.parse(body);
        return await this.processWebhookPayload(c, payload);
      } else {
        // No signature verification - parse body directly
        const payload = await c.req.json();
        return await this.processWebhookPayload(c, payload);
      }
    } catch (error) {
      this.logger.error('Error processing webhook', {
        action: 'webhook_event',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.text('Internal Server Error', 500);
    }
  }

  private async processWebhookPayload(c: Context, payload: any): Promise<Response> {
    try {
      // Validate payload structure
      const validationResult = whatsappWebhookSchema.safeParse(payload);
      if (!validationResult.success) {
        this.logger.warn('Invalid webhook payload structure', {
          action: 'webhook_event',
          errors: validationResult.error.errors,
        });
        return c.text('Bad Request', 400);
      }

      this.logger.info('Processing webhook payload', {
        action: 'webhook_event',
        object: payload.object,
        entryCount: payload.entry?.length || 0,
      });

      // Process each entry in the webhook
      for (const entry of payload.entry) {
        for (const change of entry.changes) {
          await this.processWebhookChange(change);
        }
      }

      return c.text('OK', 200);
    } catch (error) {
      this.logger.error('Error processing webhook payload', {
        action: 'webhook_event',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.text('Internal Server Error', 500);
    }
  }

  private async processWebhookChange(change: any): Promise<void> {
    this.logger.info('Processing webhook change', {
      action: 'process_webhook_change',
      field: change.field,
      hasMessages: !!change.value.messages,
      hasStatuses: !!change.value.statuses,
      messageCount: change.value.messages?.length || 0,
      statusCount: change.value.statuses?.length || 0,
    });

    try {
      // Process incoming messages
      if (change.value.messages) {
        for (const message of change.value.messages) {
          await this.whatsAppService.handleIncomingMessage(message, change.value.metadata);
        }
      }

      // Process message statuses
      if (change.value.statuses) {
        for (const status of change.value.statuses) {
          await this.whatsAppService.handleMessageStatus(status);
        }
      }
    } catch (error) {
      this.logger.error('Error processing webhook change', {
        action: 'process_webhook_change',
        field: change.field,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      // Don't throw error to avoid webhook retries for processing errors
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(c: Context): Promise<Response> {
    return c.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'whatsapp-flows-hono',
    });
  }

  /**
   * Get webhook info for debugging
   */
  async getWebhookInfo(c: Context): Promise<Response> {
    if (this.config.app.environment === 'production') {
      return c.text('Not Found', 404);
    }

    return c.json({
      webhookUrl: `${this.config.app.url}/webhook/whatsapp`,
      verifyToken: this.config.whatsapp.verifyToken ? 'configured' : 'not configured',
      webhookSecret: this.config.whatsapp.webhookSecret ? 'configured' : 'not configured',
      phoneNumberId: this.config.whatsapp.phoneNumberId,
      environment: this.config.app.environment,
    });
  }
}
