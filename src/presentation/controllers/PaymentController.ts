import { Context } from 'hono';
import type { DpoService } from '@/application/services/DpoService';
import type { PaymentService } from '@/application/services/PaymentService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';

export class PaymentController {
  constructor(
    private dpoService: DpoService,
    private paymentService: PaymentService,
    private logger: Logger,
    private config: AppConfig,
  ) {}

  /**
   * Handle DPO Pay webhook callbacks
   */
  async handleDpoCallback(c: Context): Promise<Response> {
    this.logger.info('DPO payment callback received', {
      action: 'dpo_callback',
      method: c.req.method,
      contentType: c.req.header('content-type'),
    });

    try {
      // Parse callback data
      const body = await c.req.text();
      const params = new URLSearchParams(body);

      const transactionToken = params.get('TransactionToken');
      const companyRef = params.get('CompanyRef');
      const result = params.get('Result');
      const resultExplanation = params.get('ResultExplanation');

      this.logger.info('DPO callback parameters', {
        action: 'dpo_callback',
        transactionToken: transactionToken ? 'present' : 'missing',
        companyRef: companyRef ? 'present' : 'missing',
        result,
        resultExplanation,
      });

      if (!transactionToken) {
        this.logger.warn('Missing transaction token in DPO callback', {
          action: 'dpo_callback',
        });
        return c.text('Bad Request: Missing transaction token', 400);
      }

      // Map DPO result to status
      let status = 'unknown';
      if (result === '000') {
        status = 'completed';
      } else if (result === '901') {
        status = 'cancelled';
      } else {
        status = 'failed';
      }

      // Process the callback
      await this.dpoService.handlePaymentCallback(
        transactionToken,
        status,
        companyRef || undefined,
      );

      this.logger.info('DPO callback processed successfully', {
        action: 'dpo_callback',
        transactionToken,
        status,
      });

      return c.text('OK', 200);
    } catch (error) {
      this.logger.error('Error processing DPO callback', {
        action: 'dpo_callback',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.text('Internal Server Error', 500);
    }
  }

  /**
   * Handle payment success redirect
   */
  async handlePaymentSuccess(c: Context): Promise<Response> {
    const transactionToken = c.req.query('TransactionToken');
    const companyRef = c.req.query('CompanyRef');

    this.logger.info('Payment success redirect', {
      action: 'payment_success',
      transactionToken: transactionToken ? 'present' : 'missing',
      companyRef: companyRef ? 'present' : 'missing',
    });

    if (!transactionToken) {
      return c.html(`
        <html>
          <head><title>Payment Error</title></head>
          <body>
            <h1>Payment Error</h1>
            <p>Invalid payment reference. Please contact support.</p>
          </body>
        </html>
      `, 400);
    }

    try {
      // Verify payment status
      const payment = await this.paymentService.getPaymentByDpoToken(transactionToken);
      if (!payment) {
        return c.html(`
          <html>
            <head><title>Payment Not Found</title></head>
            <body>
              <h1>Payment Not Found</h1>
              <p>Payment reference not found. Please contact support.</p>
            </body>
          </html>
        `, 404);
      }

      return c.html(`
        <html>
          <head>
            <title>Payment Successful</title>
            <style>
              body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
              .success { color: #28a745; }
              .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
          </head>
          <body>
            <h1 class="success">✅ Payment Successful!</h1>
            <p>Thank you for your payment. Your transaction has been processed successfully.</p>
            
            <div class="info">
              <h3>Payment Details:</h3>
              <p><strong>Product:</strong> ${payment.productName}</p>
              <p><strong>Amount:</strong> $${payment.amount} ${payment.currency}</p>
              <p><strong>Status:</strong> ${payment.status}</p>
              <p><strong>Reference:</strong> ${payment.dpoPaymentReference || 'Processing'}</p>
            </div>
            
            <p>You can now close this window and return to WhatsApp.</p>
          </body>
        </html>
      `);
    } catch (error) {
      this.logger.error('Error handling payment success', {
        action: 'payment_success',
        transactionToken,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.html(`
        <html>
          <head><title>Payment Error</title></head>
          <body>
            <h1>Payment Processing Error</h1>
            <p>There was an error processing your payment. Please contact support.</p>
          </body>
        </html>
      `, 500);
    }
  }

  /**
   * Handle payment cancellation
   */
  async handlePaymentCancel(c: Context): Promise<Response> {
    const transactionToken = c.req.query('TransactionToken');

    this.logger.info('Payment cancellation', {
      action: 'payment_cancel',
      transactionToken: transactionToken ? 'present' : 'missing',
    });

    if (transactionToken) {
      try {
        await this.dpoService.cancelPayment(transactionToken);
      } catch (error) {
        this.logger.error('Error cancelling payment', {
          action: 'payment_cancel',
          transactionToken,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return c.html(`
      <html>
        <head>
          <title>Payment Cancelled</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .warning { color: #ffc107; }
          </style>
        </head>
        <body>
          <h1 class="warning">⚠️ Payment Cancelled</h1>
          <p>Your payment has been cancelled. No charges have been made.</p>
          <p>You can return to WhatsApp to try again or contact support if you need assistance.</p>
        </body>
      </html>
    `);
  }

  /**
   * Get payment status (for debugging)
   */
  async getPaymentStatus(c: Context): Promise<Response> {
    if (this.config.app.environment === 'production') {
      return c.text('Not Found', 404);
    }

    const paymentId = c.req.param('paymentId');
    if (!paymentId) {
      return c.json({ error: 'Payment ID required' }, 400);
    }

    try {
      const payment = await this.paymentService.getPaymentById(paymentId);
      if (!payment) {
        return c.json({ error: 'Payment not found' }, 404);
      }

      return c.json({
        id: payment.id,
        status: payment.status,
        amount: payment.amount,
        currency: payment.currency,
        productName: payment.productName,
        dpoTransactionToken: payment.dpoTransactionToken,
        dpoPaymentReference: payment.dpoPaymentReference,
        paymentUrl: payment.paymentUrl,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      });
    } catch (error) {
      this.logger.error('Error getting payment status', {
        action: 'get_payment_status',
        paymentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.json({ error: 'Internal server error' }, 500);
    }
  }

  /**
   * Get payment methods
   */
  async getPaymentMethods(c: Context): Promise<Response> {
    try {
      const methods = await this.dpoService.getPaymentMethods();
      return c.json({ paymentMethods: methods });
    } catch (error) {
      this.logger.error('Error getting payment methods', {
        action: 'get_payment_methods',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return c.json({ error: 'Failed to get payment methods' }, 500);
    }
  }

  /**
   * Health check for payment service
   */
  async healthCheck(c: Context): Promise<Response> {
    try {
      const stats = await this.paymentService.getPaymentStats();
      return c.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'payment-service',
        stats,
      });
    } catch (error) {
      return c.json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'payment-service',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
}
