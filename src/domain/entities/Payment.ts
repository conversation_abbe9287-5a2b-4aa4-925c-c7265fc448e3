export type PaymentStatus =
  | 'pending'
  | 'initiated'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'expired';

export interface PaymentData {
  id: string;
  userId: string;
  dpoTransactionToken: string;
  dpoPaymentReference?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  productName: string;
  productDescription?: string;
  customerEmail: string;
  customerPhone: string;
  paymentUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  failedAt?: Date;
  failureReason?: string;
}

export class Payment {
  public readonly id: string;
  public readonly userId: string;
  public readonly dpoTransactionToken: string;
  public readonly dpoPaymentReference?: string;
  public readonly amount: number;
  public readonly currency: string;
  public readonly status: PaymentStatus;
  public readonly productName: string;
  public readonly productDescription?: string;
  public readonly customerEmail: string;
  public readonly customerPhone: string;
  public readonly paymentUrl?: string;
  public readonly createdAt: Date;
  public readonly updatedAt: Date;
  public readonly completedAt?: Date;
  public readonly failedAt?: Date;
  public readonly failureReason?: string;

  constructor(data: PaymentData) {
    // Validation
    if (!data.id || data.id.trim() === '') {
      throw new Error('Payment ID is required');
    }

    if (!data.userId || data.userId.trim() === '') {
      throw new Error('User ID is required');
    }

    if (data.amount <= 0) {
      throw new Error('Amount must be positive');
    }

    if (!data.currency || !this.isValidCurrency(data.currency)) {
      throw new Error('Invalid currency');
    }

    if (!data.customerEmail || !this.isValidEmail(data.customerEmail)) {
      throw new Error('Invalid email format');
    }

    if (!data.customerPhone || !this.isValidPhoneNumber(data.customerPhone)) {
      throw new Error('Invalid phone number format');
    }

    if (!data.productName || data.productName.trim() === '') {
      throw new Error('Product name is required');
    }

    // Assign properties
    this.id = data.id;
    this.userId = data.userId;
    this.dpoTransactionToken = data.dpoTransactionToken;
    this.dpoPaymentReference = data.dpoPaymentReference;
    this.amount = data.amount;
    this.currency = data.currency;
    this.status = data.status;
    this.productName = data.productName;
    this.productDescription = data.productDescription;
    this.customerEmail = data.customerEmail;
    this.customerPhone = data.customerPhone;
    this.paymentUrl = data.paymentUrl;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.completedAt = data.completedAt;
    this.failedAt = data.failedAt;
    this.failureReason = data.failureReason;
  }

  // Business logic methods
  isPending(): boolean {
    return this.status === 'pending';
  }

  isCompleted(): boolean {
    return this.status === 'completed';
  }

  isFailed(): boolean {
    return this.status === 'failed';
  }

  getFormattedAmount(): string {
    // Format based on currency
    const currencySymbols: Record<string, string> = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'ZAR': 'R',
      'KES': 'KSh',
      'UGX': 'USh',
      'TZS': 'TSh',
    };

    const symbol = currencySymbols[this.currency] || this.currency;
    return `${symbol}${this.amount.toFixed(2)} ${this.currency}`;
  }

  isExpired(): boolean {
    if (this.status === 'completed') {
      return false; // Completed payments never expire
    }

    const expirationTime = 15 * 60 * 1000; // 15 minutes in milliseconds
    const now = Date.now();
    const createdTime = this.createdAt.getTime();

    return (now - createdTime) > expirationTime;
  }

  toJSON(): PaymentData {
    return {
      id: this.id,
      userId: this.userId,
      dpoTransactionToken: this.dpoTransactionToken,
      dpoPaymentReference: this.dpoPaymentReference,
      amount: this.amount,
      currency: this.currency,
      status: this.status,
      productName: this.productName,
      productDescription: this.productDescription,
      customerEmail: this.customerEmail,
      customerPhone: this.customerPhone,
      paymentUrl: this.paymentUrl,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      completedAt: this.completedAt,
      failedAt: this.failedAt,
      failureReason: this.failureReason,
    };
  }

  // Private validation methods
  private isValidCurrency(currency: string): boolean {
    // Common currency codes
    const validCurrencies = ['USD', 'EUR', 'GBP', 'ZAR', 'KES', 'UGX', 'TZS'];
    return validCurrencies.includes(currency.toUpperCase());
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhoneNumber(phone: string): boolean {
    // Basic international phone number validation
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
  }
}

export interface CreatePaymentRequest {
  userId: string;
  amount: number;
  currency: string;
  productName: string;
  productDescription?: string;
  customerEmail: string;
  customerPhone: string;
}

export interface UpdatePaymentRequest {
  status?: PaymentStatus;
  dpoPaymentReference?: string;
  paymentUrl?: string;
  failureReason?: string;
}
