export type RegistrationStatus = 'pending' | 'completed' | 'failed';

export interface UserData {
  id: string;
  whatsappPhoneNumber: string;
  name?: string;
  email?: string;
  registrationStatus: RegistrationStatus;
  createdAt: Date;
  updatedAt: Date;
}

export class User {
  public readonly id: string;
  public readonly whatsappPhoneNumber: string;
  public readonly name?: string;
  public readonly email?: string;
  public readonly registrationStatus: RegistrationStatus;
  public readonly createdAt: Date;
  public readonly updatedAt: Date;

  constructor(data: UserData) {
    // Validation
    if (!data.id || data.id.trim() === '') {
      throw new Error('User ID is required');
    }

    if (!data.whatsappPhoneNumber || !this.isValidPhoneNumber(data.whatsappPhoneNumber)) {
      throw new Error('Invalid phone number format');
    }

    if (data.email && !this.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    if (!['pending', 'completed', 'failed'].includes(data.registrationStatus)) {
      throw new Error('Invalid registration status');
    }

    if (data.name && data.name.length > 100) {
      throw new Error('Name too long');
    }

    // Assign properties
    this.id = data.id;
    this.whatsappPhoneNumber = data.whatsappPhoneNumber;
    this.name = data.name;
    this.email = data.email;
    this.registrationStatus = data.registrationStatus;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
  }

  // Business logic methods
  isRegistrationComplete(): boolean {
    return this.registrationStatus === 'completed';
  }

  hasCompleteProfile(): boolean {
    return !!(this.name && this.email);
  }

  getDisplayName(): string {
    return this.name || this.whatsappPhoneNumber;
  }

  toJSON(): any {
    return {
      id: this.id,
      whatsappPhoneNumber: this.whatsappPhoneNumber,
      name: this.name,
      email: this.email,
      registrationStatus: this.registrationStatus,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
    };
  }

  // Private validation methods
  private isValidPhoneNumber(phone: string): boolean {
    // Basic international phone number validation
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

export interface CreateUserRequest {
  whatsappPhoneNumber: string;
  name?: string;
  email?: string;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  registrationStatus?: RegistrationStatus;
}
