export interface FlowSession {
  id: string;
  userId: string;
  flowType: FlowType;
  flowId: string;
  status: FlowSessionStatus;
  currentStep: string;
  sessionData: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  expiresAt: Date;
}

export type FlowType = 'customer_registration' | 'payment';

export type FlowSessionStatus = 
  | 'active'
  | 'completed'
  | 'expired'
  | 'cancelled'
  | 'failed';

export interface CreateFlowSessionRequest {
  userId: string;
  flowType: FlowType;
  flowId: string;
  currentStep: string;
  sessionData?: Record<string, any>;
  expiresAt: Date;
}

export interface UpdateFlowSessionRequest {
  status?: FlowSessionStatus;
  currentStep?: string;
  sessionData?: Record<string, any>;
  expiresAt?: Date;
}
