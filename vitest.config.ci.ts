import { defineConfig } from 'vitest/config';
import path from 'path';

// CI-specific configuration that uses Node.js environment for coverage
export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/setup.ci.ts'],
    include: ['tests/**/*.test.ts'],
    exclude: ['node_modules', 'dist', '.wrangler'],
    coverage: {
      enabled: true,
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/**',
        'dist/**',
        '.wrangler/**',
        'tests/**',
        'drizzle/**',
        'flows/**',
        'docs/**',
        '**/*.d.ts',
        '**/*.config.ts',
        '**/index.ts', // Entry points
      ],
      include: ['src/**/*.ts'],
      thresholds: {
        global: {
          branches: 60,
          functions: 60,
          lines: 60,
          statements: 60,
        },
        // Per-directory thresholds
        'src/application/': {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70,
        },
        'src/domain/': {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
        'src/shared/utils/': {
          branches: 75,
          functions: 75,
          lines: 75,
          statements: 75,
        },
      },
    },
    testTimeout: 15000,
    hookTimeout: 15000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@tests': path.resolve(__dirname, './tests'),
    },
  },
  esbuild: {
    target: 'es2022',
  },
});
