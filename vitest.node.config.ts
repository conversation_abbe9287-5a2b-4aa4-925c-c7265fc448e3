import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    name: 'node-tests',
    environment: 'node',
    globals: true,
    setupFiles: ['./tests/setup.node.ts'],
    include: [
      'tests/unit/**/*.test.ts',
      'tests/integration/**/*.test.ts',
    ],
    exclude: [
      'tests/unit/infrastructure/repositories/**/*.test.ts', // Skip D1-specific tests
      'tests/unit/presentation/controllers/**/*.test.ts', // Skip Workers-specific tests
      'node_modules/**',
      'dist/**',
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/**',
        'tests/**',
        'dist/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/index.ts', // Entry points
        'src/infrastructure/database/**', // Database schema
        'src/shared/types/**', // Type definitions
      ],
      include: [
        'src/**/*.ts',
      ],
      thresholds: {
        global: {
          lines: 60,
          functions: 60,
          branches: 60,
          statements: 60,
        },
        'src/application/': {
          lines: 70,
          functions: 70,
          branches: 70,
          statements: 70,
        },
        'src/domain/': {
          lines: 80,
          functions: 80,
          branches: 80,
          statements: 80,
        },
        'src/shared/utils/': {
          lines: 75,
          functions: 75,
          branches: 75,
          statements: 75,
        },
      },
    },
    testTimeout: 10000,
    hookTimeout: 10000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@tests': path.resolve(__dirname, './tests'),
    },
  },
  define: {
    // Mock Cloudflare Workers globals for Node.js environment
    'globalThis.fetch': 'globalThis.fetch',
    'globalThis.Request': 'globalThis.Request',
    'globalThis.Response': 'globalThis.Response',
    'globalThis.Headers': 'globalThis.Headers',
    'globalThis.URL': 'globalThis.URL',
    'globalThis.URLSearchParams': 'globalThis.URLSearchParams',
  },
});
