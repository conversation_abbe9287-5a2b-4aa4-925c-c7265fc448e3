{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": false, "lib": ["ESNext", "WebWorker"], "types": ["@cloudflare/workers-types", "node"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/domain/*": ["src/domain/*"], "@/application/*": ["src/application/*"], "@/infrastructure/*": ["src/infrastructure/*"], "@/presentation/*": ["src/presentation/*"], "@/shared/*": ["src/shared/*"]}, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "drizzle.config.ts"], "exclude": ["node_modules", "dist", ".wrangler"]}