#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 WhatsApp Flow Application - Cloudflare Workers Test Runner');
console.log('============================================================\n');

// Check if dependencies are installed
console.log('📦 Checking dependencies...');
try {
  if (!fs.existsSync('node_modules')) {
    console.log('❌ node_modules not found. Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  } else {
    console.log('✅ Dependencies found');
  }

  // Check for Cloudflare Workers testing dependencies
  const requiredDeps = [
    '@cloudflare/vitest-pool-workers',
    'vitest',
    '@cloudflare/workers-types'
  ];

  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };

  for (const dep of requiredDeps) {
    if (!allDeps[dep]) {
      console.log(`❌ Missing required dependency: ${dep}`);
      console.log('Installing Cloudflare Workers testing dependencies...');
      execSync('npm install @cloudflare/vitest-pool-workers@latest --save-dev', { stdio: 'inherit' });
      break;
    }
  }

  console.log('✅ Cloudflare Workers testing dependencies verified');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Check TypeScript compilation
console.log('\n🔍 Checking TypeScript compilation...');
try {
  execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.log('⚠️  TypeScript compilation issues found (continuing with tests)');
  // Don't exit, continue with tests
}

// Check Cloudflare Workers configuration
console.log('\n⚙️  Checking Cloudflare Workers configuration...');
try {
  if (!fs.existsSync('wrangler.toml')) {
    console.log('⚠️  wrangler.toml not found, creating basic configuration...');
    const basicWranglerConfig = `
name = "whatsapp-flows-hono"
compatibility_date = "2024-09-09"
compatibility_flags = ["nodejs_compat"]

[env.test]
WHATSAPP_VERIFY_TOKEN = "test_verify_token"
WHATSAPP_ACCESS_TOKEN = "test_access_token"
WHATSAPP_PHONE_NUMBER_ID = "test_phone_id"
DPO_COMPANY_TOKEN = "test_dpo_token"
ENVIRONMENT = "test"
`;
    fs.writeFileSync('wrangler.toml', basicWranglerConfig.trim());
  }

  console.log('✅ Cloudflare Workers configuration verified');
} catch (error) {
  console.warn('⚠️  Could not verify Cloudflare Workers configuration:', error.message);
}

// Generate types for Cloudflare Workers
console.log('\n🔧 Generating Cloudflare Workers types...');
try {
  execSync('npx wrangler types', { stdio: 'pipe' });
  console.log('✅ Types generated successfully');
} catch (error) {
  console.warn('⚠️  Could not generate types:', error.message);
}

// Run tests
console.log('\n🧪 Running Cloudflare Workers test suite...');
try {
  // First run a simple test to verify setup
  console.log('Running basic validation tests...');
  execSync('npx vitest run tests/simple.test.ts --reporter=verbose', {
    stdio: 'inherit',
    timeout: 30000
  });

  console.log('\n✅ Basic tests passed! Running Cloudflare Workers tests...');

  // Run Cloudflare Workers specific tests
  console.log('Running Cloudflare Workers unit tests...');
  execSync('npx vitest run tests/unit/cloudflare-workers --reporter=verbose', {
    stdio: 'inherit',
    timeout: 60000
  });

  console.log('Running Cloudflare Workers integration tests...');
  execSync('npx vitest run tests/integration/cloudflare-workers --reporter=verbose', {
    stdio: 'inherit',
    timeout: 60000
  });

  console.log('\n✅ Cloudflare Workers tests passed! Running full test suite...');

  // Run all tests with coverage
  execSync('npx vitest run --coverage --reporter=verbose', {
    stdio: 'inherit',
    timeout: 120000
  });

  console.log('\n🎉 All tests completed successfully!');

} catch (error) {
  console.error('\n❌ Tests failed:', error.message);

  // Try to run tests without coverage to see what's failing
  console.log('\n🔍 Running tests without coverage for debugging...');
  try {
    execSync('npx vitest run --reporter=verbose', {
      stdio: 'inherit',
      timeout: 60000
    });
  } catch (debugError) {
    console.error('❌ Tests still failing:', debugError.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Check that @cloudflare/vitest-pool-workers is installed');
    console.log('2. Verify wrangler.toml configuration');
    console.log('3. Ensure all environment variables are set');
    console.log('4. Check tests/tsconfig.json for correct types');
  }

  process.exit(1);
}

// Check coverage report
console.log('\n📊 Checking coverage report...');
try {
  if (fs.existsSync('coverage/coverage-summary.json')) {
    const coverage = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
    const { lines, functions, branches, statements } = coverage.total;
    
    console.log('Coverage Summary:');
    console.log(`  Lines: ${lines.pct}%`);
    console.log(`  Functions: ${functions.pct}%`);
    console.log(`  Branches: ${branches.pct}%`);
    console.log(`  Statements: ${statements.pct}%`);
    
    const minCoverage = 60;
    if (lines.pct >= minCoverage && functions.pct >= minCoverage && 
        branches.pct >= minCoverage && statements.pct >= minCoverage) {
      console.log(`\n✅ Coverage meets minimum threshold of ${minCoverage}%`);
    } else {
      console.log(`\n⚠️  Coverage below minimum threshold of ${minCoverage}%`);
    }
  } else {
    console.log('⚠️  Coverage report not found');
  }
} catch (error) {
  console.log('⚠️  Could not read coverage report:', error.message);
}

console.log('\n🏁 Cloudflare Workers test run completed!');
console.log('\nNext steps:');
console.log('- Review coverage report in coverage/index.html');
console.log('- Run `npm run test:watch` for development');
console.log('- Run `npm run test:ui` for interactive testing');
console.log('- Run `npm run test:cloudflare` for Cloudflare Workers specific tests');
console.log('- Check docs/cloudflare-workers-testing.md for detailed guide');
