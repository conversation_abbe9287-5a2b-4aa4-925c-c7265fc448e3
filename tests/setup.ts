import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { env, SELF } from 'cloudflare:test';

beforeAll(async () => {
  // Initialize test database with Cloudflare D1
  // The database is automatically set up by the Workers testing environment
  await setupTestDatabase();
});

beforeEach(async () => {
  // Clean database before each test using D1
  await cleanTestDatabase();
});

// Database setup and cleanup functions for D1
async function setupTestDatabase() {
  try {
    // Create tables one by one with single-line SQL to avoid parsing issues
    await env.TEST_DB.exec(`CREATE TABLE IF NOT EXISTS users (id TEXT PRIMARY KEY, whatsapp_phone_number TEXT NOT NULL UNIQUE, name TEXT, email TEXT, registration_status TEXT DEFAULT 'pending' NOT NULL, created_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL)`);

    await env.TEST_DB.exec(`CREATE TABLE IF NOT EXISTS payments (id TEXT PRIMARY KEY, user_id TEXT NOT NULL, dpo_transaction_token TEXT NOT NULL, dpo_payment_reference TEXT, amount REAL NOT NULL, currency TEXT NOT NULL, status TEXT DEFAULT 'pending' NOT NULL, product_name TEXT NOT NULL, product_description TEXT, customer_email TEXT NOT NULL, customer_phone TEXT NOT NULL, payment_url TEXT, created_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, completed_at TEXT, failed_at TEXT, failure_reason TEXT, FOREIGN KEY (user_id) REFERENCES users(id))`);

    await env.TEST_DB.exec(`CREATE TABLE IF NOT EXISTS flow_sessions (id TEXT PRIMARY KEY, user_id TEXT NOT NULL, flow_type TEXT NOT NULL, flow_id TEXT NOT NULL, status TEXT DEFAULT 'active' NOT NULL, current_step TEXT NOT NULL, session_data TEXT, created_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, completed_at TEXT, expires_at TEXT NOT NULL, FOREIGN KEY (user_id) REFERENCES users(id))`);

    await env.TEST_DB.exec(`CREATE TABLE IF NOT EXISTS audit_logs (id TEXT PRIMARY KEY, user_id TEXT, action TEXT NOT NULL, entity_type TEXT NOT NULL, entity_id TEXT NOT NULL, old_values TEXT, new_values TEXT, metadata TEXT, ip_address TEXT, user_agent TEXT, created_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, FOREIGN KEY (user_id) REFERENCES users(id))`);

    await env.TEST_DB.exec(`CREATE TABLE IF NOT EXISTS rate_limits (id TEXT PRIMARY KEY, identifier TEXT NOT NULL, endpoint TEXT NOT NULL, request_count INTEGER DEFAULT 0 NOT NULL, window_start TEXT NOT NULL, created_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TEXT DEFAULT CURRENT_TIMESTAMP NOT NULL)`);

    // Create indexes
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_users_phone ON users(whatsapp_phone_number)`);
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_payments_user ON payments(user_id)`);
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status)`);
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_flow_sessions_user ON flow_sessions(user_id)`);
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_flow_sessions_status ON flow_sessions(status)`);
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs(entity_type, entity_id)`);
    await env.TEST_DB.exec(`CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON rate_limits(identifier, endpoint)`);
  } catch (error) {
    console.warn('Database setup failed, continuing with tests:', error);
  }
}

async function cleanTestDatabase() {
  try {
    // Clean all tables in correct order (respecting foreign keys)
    // Use individual statements to avoid SQL parsing issues
    await env.TEST_DB.exec(`DELETE FROM rate_limits`);
    await env.TEST_DB.exec(`DELETE FROM audit_logs`);
    await env.TEST_DB.exec(`DELETE FROM flow_sessions`);
    await env.TEST_DB.exec(`DELETE FROM payments`);
    await env.TEST_DB.exec(`DELETE FROM users`);
  } catch (error) {
    console.warn('Database cleanup failed:', error);
  }
}

// Export test utilities
export { env, SELF };

// Test utilities
export const createTestUser = (overrides: Partial<any> = {}) => {
  return {
    id: 'test-user-1',
    whatsappPhoneNumber: '+1234567890',
    name: 'Test User',
    email: '<EMAIL>',
    registrationStatus: 'completed' as const,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
};

export const createTestPayment = (overrides: Partial<any> = {}) => {
  return {
    id: 'test-payment-1',
    userId: 'test-user-1',
    dpoTransactionToken: 'TEST_TOKEN_123',
    amount: 99.99,
    currency: 'USD',
    status: 'pending' as const,
    productName: 'Test Product',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
};

export const createTestFlowSession = (overrides: Partial<any> = {}) => {
  return {
    id: 'test-session-1',
    userId: 'test-user-1',
    flowType: 'customer_registration' as const,
    flowId: 'test-flow-1',
    status: 'active' as const,
    currentStep: 'start',
    sessionData: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
    ...overrides,
  };
};

// Mock environment for tests
export const mockEnv = {
  DB: env.TEST_DB,
  WHATSAPP_VERIFY_TOKEN: 'test_verify_token',
  WHATSAPP_ACCESS_TOKEN: 'test_access_token',
  WHATSAPP_PHONE_NUMBER_ID: 'test_phone_id',
  WHATSAPP_WEBHOOK_SECRET: 'test_webhook_secret',
  DPO_COMPANY_TOKEN: 'test_dpo_token',
  DPO_SERVICE_TYPE: 'test_service',
  DPO_PAYMENT_URL: 'https://secure.3gdirectpay.com',
  DPO_PAYMENT_API: 'https://secure.3gdirectpay.com/payv2.php',
  APP_URL: 'https://test.workers.dev',
  ENVIRONMENT: 'test',
  JWT_SECRET: 'test_jwt_secret_32_characters_long',
  ENCRYPTION_KEY: 'test_encryption_key_32_chars_long',
  LOG_LEVEL: 'error',
  ENABLE_REQUEST_LOGGING: 'false',
  RATE_LIMIT_REQUESTS_PER_MINUTE: '60',
  RATE_LIMIT_BURST_SIZE: '10',
  CUSTOMER_REGISTRATION_FLOW_ID: 'test_reg_flow',
  PAYMENT_FLOW_ID: 'test_payment_flow',
  DEFAULT_CURRENCY: 'USD',
  PAYMENT_TIMEOUT_MINUTES: '15',
  SESSION_TIMEOUT_MINUTES: '30',
};
