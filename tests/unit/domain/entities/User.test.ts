import { describe, it, expect } from 'vitest';
import { User } from '@/domain/entities/User';

describe('User Entity', () => {
  describe('constructor', () => {
    it('should create user with required fields', () => {
      const userData = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'pending' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const user = new User(userData);

      expect(user.id).toBe(userData.id);
      expect(user.whatsappPhoneNumber).toBe(userData.whatsappPhoneNumber);
      expect(user.registrationStatus).toBe(userData.registrationStatus);
      expect(user.createdAt).toBe(userData.createdAt);
      expect(user.updatedAt).toBe(userData.updatedAt);
      expect(user.name).toBeUndefined();
      expect(user.email).toBeUndefined();
    });

    it('should create user with all fields', () => {
      const userData = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: '<PERSON>',
        email: '<EMAIL>',
        registrationStatus: 'completed' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const user = new User(userData);

      expect(user.id).toBe(userData.id);
      expect(user.whatsappPhoneNumber).toBe(userData.whatsappPhoneNumber);
      expect(user.name).toBe(userData.name);
      expect(user.email).toBe(userData.email);
      expect(user.registrationStatus).toBe(userData.registrationStatus);
    });
  });

  describe('isRegistrationComplete', () => {
    it('should return true for completed registration', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        email: '<EMAIL>',
        registrationStatus: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.isRegistrationComplete()).toBe(true);
    });

    it('should return false for pending registration', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.isRegistrationComplete()).toBe(false);
    });

    it('should return false for failed registration', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'failed',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.isRegistrationComplete()).toBe(false);
    });
  });

  describe('hasCompleteProfile', () => {
    it('should return true when user has name and email', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        email: '<EMAIL>',
        registrationStatus: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.hasCompleteProfile()).toBe(true);
    });

    it('should return false when user missing name', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        email: '<EMAIL>',
        registrationStatus: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.hasCompleteProfile()).toBe(false);
    });

    it('should return false when user missing email', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        registrationStatus: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.hasCompleteProfile()).toBe(false);
    });

    it('should return false when user missing both name and email', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.hasCompleteProfile()).toBe(false);
    });
  });

  describe('getDisplayName', () => {
    it('should return name when available', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        registrationStatus: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.getDisplayName()).toBe('John Doe');
    });

    it('should return phone number when name not available', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.getDisplayName()).toBe('+1234567890');
    });
  });

  describe('toJSON', () => {
    it('should serialize user to JSON', () => {
      const userData = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        email: '<EMAIL>',
        registrationStatus: 'completed' as const,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z'),
      };

      const user = new User(userData);
      const json = user.toJSON();

      expect(json).toEqual({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        email: '<EMAIL>',
        registrationStatus: 'completed',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      });
    });

    it('should serialize user with minimal data', () => {
      const userData = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'pending' as const,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z'),
      };

      const user = new User(userData);
      const json = user.toJSON();

      expect(json).toEqual({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: undefined,
        email: undefined,
        registrationStatus: 'pending',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      });
    });
  });

  describe('validation', () => {
    it('should validate phone number format', () => {
      expect(() => new User({
        id: 'user-123',
        whatsappPhoneNumber: 'invalid-phone',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Invalid phone number format');
    });

    it('should validate email format when provided', () => {
      expect(() => new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        email: 'invalid-email',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Invalid email format');
    });

    it('should validate registration status', () => {
      expect(() => new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'invalid-status' as any,
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Invalid registration status');
    });

    it('should require non-empty ID', () => {
      expect(() => new User({
        id: '',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('User ID is required');
    });

    it('should validate name length when provided', () => {
      expect(() => new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'a'.repeat(101), // Too long
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Name too long');
    });
  });

  describe('edge cases', () => {
    it('should handle international phone numbers', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+447700900123', // UK number
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.whatsappPhoneNumber).toBe('+447700900123');
    });

    it('should handle names with special characters', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: "O'Connor-Smith Jr.",
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.name).toBe("O'Connor-Smith Jr.");
    });

    it('should handle unicode in names', () => {
      const user = new User({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: '张三',
        registrationStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(user.name).toBe('张三');
    });
  });
});
