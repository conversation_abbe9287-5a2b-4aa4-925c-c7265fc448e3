import { describe, it, expect } from 'vitest';
import { Payment } from '@/domain/entities/Payment';

describe('Payment Entity', () => {
  describe('constructor', () => {
    it('should create payment with required fields', () => {
      const paymentData = {
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending' as const,
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const payment = new Payment(paymentData);

      expect(payment.id).toBe(paymentData.id);
      expect(payment.userId).toBe(paymentData.userId);
      expect(payment.amount).toBe(paymentData.amount);
      expect(payment.currency).toBe(paymentData.currency);
      expect(payment.status).toBe(paymentData.status);
      expect(payment.productName).toBe(paymentData.productName);
    });

    it('should create payment with all fields', () => {
      const paymentData = {
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        dpoPaymentReference: 'ref-123',
        amount: 99.99,
        currency: 'USD',
        status: 'completed' as const,
        productName: 'Premium Plan',
        productDescription: 'Advanced features',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        paymentUrl: 'https://secure.3gdirectpay.com/?ID=token-123',
        createdAt: new Date(),
        updatedAt: new Date(),
        completedAt: new Date(),
      };

      const payment = new Payment(paymentData);

      expect(payment.dpoPaymentReference).toBe(paymentData.dpoPaymentReference);
      expect(payment.productDescription).toBe(paymentData.productDescription);
      expect(payment.paymentUrl).toBe(paymentData.paymentUrl);
      expect(payment.completedAt).toBe(paymentData.completedAt);
    });
  });

  describe('isPending', () => {
    it('should return true for pending status', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.isPending()).toBe(true);
    });

    it('should return false for non-pending status', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'completed',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.isPending()).toBe(false);
    });
  });

  describe('isCompleted', () => {
    it('should return true for completed status', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'completed',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.isCompleted()).toBe(true);
    });

    it('should return false for non-completed status', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.isCompleted()).toBe(false);
    });
  });

  describe('isFailed', () => {
    it('should return true for failed status', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'failed',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.isFailed()).toBe(true);
    });

    it('should return false for non-failed status', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'completed',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.isFailed()).toBe(false);
    });
  });

  describe('getFormattedAmount', () => {
    it('should format amount with currency', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.getFormattedAmount()).toBe('$99.99 USD');
    });

    it('should handle different currencies', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 85.50,
        currency: 'EUR',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.getFormattedAmount()).toBe('€85.50 EUR');
    });

    it('should handle whole numbers', () => {
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 100,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(payment.getFormattedAmount()).toBe('$100.00 USD');
    });
  });

  describe('isExpired', () => {
    it('should return true for expired payment', () => {
      const oldDate = new Date(Date.now() - 20 * 60 * 1000); // 20 minutes ago
      
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: oldDate,
        updatedAt: oldDate,
      });

      expect(payment.isExpired(15)).toBe(true); // 15 minute timeout
    });

    it('should return false for non-expired payment', () => {
      const recentDate = new Date(Date.now() - 5 * 60 * 1000); // 5 minutes ago
      
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: recentDate,
        updatedAt: recentDate,
      });

      expect(payment.isExpired(15)).toBe(false); // 15 minute timeout
    });

    it('should return false for completed payment regardless of age', () => {
      const oldDate = new Date(Date.now() - 20 * 60 * 1000); // 20 minutes ago
      
      const payment = new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'completed',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: oldDate,
        updatedAt: oldDate,
      });

      expect(payment.isExpired(15)).toBe(false);
    });
  });

  describe('validation', () => {
    it('should validate positive amount', () => {
      expect(() => new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: -10, // Negative amount
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Amount must be positive');
    });

    it('should validate currency format', () => {
      expect(() => new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'INVALID', // Invalid currency
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Invalid currency');
    });

    it('should validate email format', () => {
      expect(() => new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: 'invalid-email', // Invalid email
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Invalid email format');
    });

    it('should validate phone number format', () => {
      expect(() => new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: 'invalid-phone', // Invalid phone
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Invalid phone number format');
    });

    it('should require non-empty product name', () => {
      expect(() => new Payment({
        id: 'payment-123',
        userId: 'user-123',
        dpoTransactionToken: 'token-123',
        amount: 99.99,
        currency: 'USD',
        status: 'pending',
        productName: '', // Empty product name
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        createdAt: new Date(),
        updatedAt: new Date(),
      })).toThrow('Product name is required');
    });
  });
});
