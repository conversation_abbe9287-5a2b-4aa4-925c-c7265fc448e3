import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { Logger, LogLevel } from '@/shared/utils/logger';

describe('Logger', () => {
  let logger: Logger;
  let consoleSpy: {
    error: any;
    warn: any;
    info: any;
    debug: any;
  };

  beforeEach(() => {
    // Mock console methods
    consoleSpy = {
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      info: vi.spyOn(console, 'info').mockImplementation(() => {}),
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should create logger with default info level', () => {
      logger = new Logger();
      
      logger.info('test message');
      logger.debug('debug message');
      
      expect(consoleSpy.info).toHaveBeenCalled();
      expect(consoleSpy.debug).not.toHaveBeenCalled();
    });

    it('should create logger with specified level', () => {
      logger = new Logger('debug');
      
      logger.debug('debug message');
      
      expect(consoleSpy.debug).toHaveBeenCalled();
    });

    it('should handle invalid log level', () => {
      logger = new Logger('invalid');
      
      logger.info('test message');
      logger.debug('debug message');
      
      expect(consoleSpy.info).toHaveBeenCalled();
      expect(consoleSpy.debug).not.toHaveBeenCalled();
    });

    it('should handle case insensitive log levels', () => {
      logger = new Logger('ERROR');
      
      logger.error('error message');
      logger.warn('warn message');
      
      expect(consoleSpy.error).toHaveBeenCalled();
      expect(consoleSpy.warn).not.toHaveBeenCalled();
    });
  });

  describe('log levels', () => {
    it('should respect ERROR level', () => {
      logger = new Logger('error');
      
      logger.error('error message');
      logger.warn('warn message');
      logger.info('info message');
      logger.debug('debug message');
      
      expect(consoleSpy.error).toHaveBeenCalled();
      expect(consoleSpy.warn).not.toHaveBeenCalled();
      expect(consoleSpy.info).not.toHaveBeenCalled();
      expect(consoleSpy.debug).not.toHaveBeenCalled();
    });

    it('should respect WARN level', () => {
      logger = new Logger('warn');
      
      logger.error('error message');
      logger.warn('warn message');
      logger.info('info message');
      logger.debug('debug message');
      
      expect(consoleSpy.error).toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.info).not.toHaveBeenCalled();
      expect(consoleSpy.debug).not.toHaveBeenCalled();
    });

    it('should respect INFO level', () => {
      logger = new Logger('info');
      
      logger.error('error message');
      logger.warn('warn message');
      logger.info('info message');
      logger.debug('debug message');
      
      expect(consoleSpy.error).toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.info).toHaveBeenCalled();
      expect(consoleSpy.debug).not.toHaveBeenCalled();
    });

    it('should respect DEBUG level', () => {
      logger = new Logger('debug');
      
      logger.error('error message');
      logger.warn('warn message');
      logger.info('info message');
      logger.debug('debug message');
      
      expect(consoleSpy.error).toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.info).toHaveBeenCalled();
      expect(consoleSpy.debug).toHaveBeenCalled();
    });
  });

  describe('message formatting', () => {
    beforeEach(() => {
      logger = new Logger('debug');
    });

    it('should format message with timestamp and level', () => {
      logger.info('test message');
      
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('"level":"INFO"')
      );
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('"message":"test message"')
      );
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('"timestamp":')
      );
    });

    it('should include context in log message', () => {
      const context = {
        userId: 'user-123',
        action: 'test_action',
        metadata: { key: 'value' },
      };
      
      logger.info('test message', context);
      
      const logCall = consoleSpy.info.mock.calls[0][0];
      const logData = JSON.parse(logCall);
      
      expect(logData.userId).toBe('user-123');
      expect(logData.action).toBe('test_action');
      expect(logData.metadata).toEqual({ key: 'value' });
    });

    it('should handle empty context', () => {
      logger.info('test message', {});
      
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('"message":"test message"')
      );
    });

    it('should handle undefined context', () => {
      logger.info('test message');
      
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('"message":"test message"')
      );
    });

    it('should format valid JSON', () => {
      logger.info('test message', { userId: 'user-123' });
      
      const logCall = consoleSpy.info.mock.calls[0][0];
      
      expect(() => JSON.parse(logCall)).not.toThrow();
    });
  });

  describe('log methods', () => {
    beforeEach(() => {
      logger = new Logger('debug');
    });

    it('should call error method correctly', () => {
      logger.error('error message');
      
      expect(consoleSpy.error).toHaveBeenCalledTimes(1);
      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('"level":"ERROR"')
      );
    });

    it('should call warn method correctly', () => {
      logger.warn('warn message');
      
      expect(consoleSpy.warn).toHaveBeenCalledTimes(1);
      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining('"level":"WARN"')
      );
    });

    it('should call info method correctly', () => {
      logger.info('info message');
      
      expect(consoleSpy.info).toHaveBeenCalledTimes(1);
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('"level":"INFO"')
      );
    });

    it('should call debug method correctly', () => {
      logger.debug('debug message');
      
      expect(consoleSpy.debug).toHaveBeenCalledTimes(1);
      expect(consoleSpy.debug).toHaveBeenCalledWith(
        expect.stringContaining('"level":"DEBUG"')
      );
    });
  });

  describe('edge cases', () => {
    beforeEach(() => {
      logger = new Logger('debug');
    });

    it('should handle special characters in message', () => {
      const message = 'Message with "quotes" and \n newlines';
      
      logger.info(message);
      
      const logCall = consoleSpy.info.mock.calls[0][0];
      const logData = JSON.parse(logCall);
      
      expect(logData.message).toBe(message);
    });

    it('should handle unicode characters', () => {
      const message = 'Unicode: 🚀 中文 emoji';
      
      logger.info(message);
      
      const logCall = consoleSpy.info.mock.calls[0][0];
      const logData = JSON.parse(logCall);
      
      expect(logData.message).toBe(message);
    });

    it('should handle complex context objects', () => {
      const context = {
        nested: {
          object: {
            with: 'deep nesting',
          },
        },
        array: [1, 2, 3],
        nullValue: null,
        undefinedValue: undefined,
      };
      
      logger.info('complex context', context);
      
      const logCall = consoleSpy.info.mock.calls[0][0];
      const logData = JSON.parse(logCall);
      
      expect(logData.nested.object.with).toBe('deep nesting');
      expect(logData.array).toEqual([1, 2, 3]);
      expect(logData.nullValue).toBeNull();
      expect(logData.undefinedValue).toBeUndefined();
    });

    it('should handle very long messages', () => {
      const longMessage = 'a'.repeat(10000);
      
      logger.info(longMessage);
      
      const logCall = consoleSpy.info.mock.calls[0][0];
      const logData = JSON.parse(logCall);
      
      expect(logData.message).toBe(longMessage);
    });
  });
});
