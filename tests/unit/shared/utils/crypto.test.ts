import { describe, it, expect, beforeEach } from 'vitest';
import { CryptoUtils } from '@/shared/utils/crypto';

describe('CryptoUtils', () => {
  let cryptoUtils: CryptoUtils;
  const testKey = 'test_encryption_key_32_chars_long';

  beforeEach(() => {
    cryptoUtils = new CryptoUtils(testKey);
  });

  describe('encrypt and decrypt', () => {
    it('should encrypt and decrypt data correctly', () => {
      const originalData = 'sensitive information';
      
      const encrypted = cryptoUtils.encrypt(originalData);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(originalData);
      
      const decrypted = cryptoUtils.decrypt(encrypted);
      expect(decrypted).toBe(originalData);
    });

    it('should produce different encrypted values for same input', () => {
      const data = 'test data';
      
      const encrypted1 = cryptoUtils.encrypt(data);
      const encrypted2 = cryptoUtils.encrypt(data);
      
      expect(encrypted1).not.toBe(encrypted2);
      expect(cryptoUtils.decrypt(encrypted1)).toBe(data);
      expect(cryptoUtils.decrypt(encrypted2)).toBe(data);
    });

    it('should handle empty strings', () => {
      const encrypted = cryptoUtils.encrypt('');
      const decrypted = cryptoUtils.decrypt(encrypted);
      
      expect(decrypted).toBe('');
    });
  });

  describe('generateRandomString', () => {
    it('should generate random string of default length', () => {
      const randomString = cryptoUtils.generateRandomString();
      
      expect(randomString).toBeDefined();
      expect(typeof randomString).toBe('string');
      expect(randomString.length).toBeGreaterThan(0);
    });

    it('should generate random string of specified length', () => {
      const length = 16;
      const randomString = cryptoUtils.generateRandomString(length);
      
      expect(randomString).toBeDefined();
      expect(typeof randomString).toBe('string');
      // Note: The actual length might vary due to hex encoding
    });

    it('should generate different strings on multiple calls', () => {
      const string1 = cryptoUtils.generateRandomString();
      const string2 = cryptoUtils.generateRandomString();
      
      expect(string1).not.toBe(string2);
    });
  });

  describe('HMAC signature', () => {
    it('should create HMAC signature correctly', () => {
      const payload = 'test payload';
      const secret = 'test secret';
      
      const signature = cryptoUtils.createHmacSignature(payload, secret);
      
      expect(signature).toBeDefined();
      expect(typeof signature).toBe('string');
      expect(signature.length).toBeGreaterThan(0);
    });

    it('should verify HMAC signature correctly', () => {
      const payload = 'test payload';
      const secret = 'test secret';
      
      const signature = cryptoUtils.createHmacSignature(payload, secret);
      const isValid = cryptoUtils.verifyHmacSignature(payload, signature, secret);
      
      expect(isValid).toBe(true);
    });

    it('should reject invalid HMAC signature', () => {
      const payload = 'test payload';
      const secret = 'test secret';
      const invalidSignature = 'invalid_signature';
      
      const isValid = cryptoUtils.verifyHmacSignature(payload, invalidSignature, secret);
      
      expect(isValid).toBe(false);
    });

    it('should reject signature with wrong secret', () => {
      const payload = 'test payload';
      const secret = 'test secret';
      const wrongSecret = 'wrong secret';
      
      const signature = cryptoUtils.createHmacSignature(payload, secret);
      const isValid = cryptoUtils.verifyHmacSignature(payload, signature, wrongSecret);
      
      expect(isValid).toBe(false);
    });

    it('should reject signature with modified payload', () => {
      const payload = 'test payload';
      const modifiedPayload = 'modified payload';
      const secret = 'test secret';
      
      const signature = cryptoUtils.createHmacSignature(payload, secret);
      const isValid = cryptoUtils.verifyHmacSignature(modifiedPayload, signature, secret);
      
      expect(isValid).toBe(false);
    });
  });

  describe('hash', () => {
    it('should create consistent hash for same input', () => {
      const data = 'test data';
      
      const hash1 = cryptoUtils.hash(data);
      const hash2 = cryptoUtils.hash(data);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toBeDefined();
      expect(typeof hash1).toBe('string');
    });

    it('should create different hashes for different inputs', () => {
      const data1 = 'test data 1';
      const data2 = 'test data 2';
      
      const hash1 = cryptoUtils.hash(data1);
      const hash2 = cryptoUtils.hash(data2);
      
      expect(hash1).not.toBe(hash2);
    });

    it('should handle empty string', () => {
      const hash = cryptoUtils.hash('');
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
    });
  });

  describe('generateUUID', () => {
    it('should generate valid UUID format', () => {
      const uuid = cryptoUtils.generateUUID();
      
      expect(uuid).toBeDefined();
      expect(typeof uuid).toBe('string');
      
      // Check UUID v4 format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      expect(uuid).toMatch(uuidRegex);
    });

    it('should generate unique UUIDs', () => {
      const uuid1 = cryptoUtils.generateUUID();
      const uuid2 = cryptoUtils.generateUUID();
      
      expect(uuid1).not.toBe(uuid2);
    });

    it('should generate multiple unique UUIDs', () => {
      const uuids = new Set();
      const count = 100;
      
      for (let i = 0; i < count; i++) {
        uuids.add(cryptoUtils.generateUUID());
      }
      
      expect(uuids.size).toBe(count);
    });
  });

  describe('edge cases', () => {
    it('should handle special characters in encryption', () => {
      const specialData = '!@#$%^&*()_+{}|:"<>?[]\\;\',./ 中文 🚀';
      
      const encrypted = cryptoUtils.encrypt(specialData);
      const decrypted = cryptoUtils.decrypt(encrypted);
      
      expect(decrypted).toBe(specialData);
    });

    it('should handle long strings in encryption', () => {
      const longData = 'a'.repeat(10000);

      const encrypted = cryptoUtils.encrypt(longData);
      const decrypted = cryptoUtils.decrypt(encrypted);

      expect(decrypted).toBe(longData);
    });

    it('should handle unicode in HMAC signature', () => {
      const unicodePayload = '测试数据 🚀 emoji';
      const secret = 'test secret';
      
      const signature = cryptoUtils.createHmacSignature(unicodePayload, secret);
      const isValid = cryptoUtils.verifyHmacSignature(unicodePayload, signature, secret);
      
      expect(isValid).toBe(true);
    });
  });
});
