import { describe, it, expect } from 'vitest';
import { env } from 'cloudflare:test';
import {
  validatePhoneNumber,
  validateEmail,
  sanitizeInput,
  isValidUUID,
  phoneNumberSchema,
  emailSchema,
  nameSchema,
  amountSchema,
  currencySchema,
  customerRegistrationSchema,
  paymentRequestSchema,
} from '@/shared/utils/validation';

describe('Validation Utils', () => {
  describe('validatePhoneNumber', () => {
    it('should validate correct phone numbers', () => {
      const validNumbers = [
        '+1234567890',
        '+12345678901',
        '1234567890',
        '+************',
        '+86138000000000',
      ];

      validNumbers.forEach(number => {
        expect(validatePhoneNumber(number)).toBe(true);
      });
    });

    it('should reject invalid phone numbers', () => {
      const invalidNumbers = [
        '',
        '123',
        '+',
        'abc123',
        '+0123456789', // starts with 0
        '+123456789012345678', // too long
        '++1234567890', // double plus
        '******-567-890', // contains dashes
      ];

      invalidNumbers.forEach(number => {
        expect(validatePhoneNumber(number)).toBe(false);
      });
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        '',
        'invalid',
        '@example.com',
        'user@',
        'user@.com',
        '<EMAIL>',
        'user@example',
        'user <EMAIL>', // space
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('sanitizeInput', () => {
    it('should remove dangerous characters', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script');
      expect(sanitizeInput('Hello <world>')).toBe('Hello world');
      expect(sanitizeInput('Test > input')).toBe('Test  input');
    });

    it('should trim whitespace', () => {
      expect(sanitizeInput('  hello world  ')).toBe('hello world');
      expect(sanitizeInput('\t\ntest\t\n')).toBe('test');
    });

    it('should handle empty and normal strings', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput('normal text')).toBe('normal text');
    });
  });

  describe('isValidUUID', () => {
    it('should validate correct UUIDs', () => {
      const validUUIDs = [
        '123e4567-e89b-12d3-a456-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
        '6ba7b811-9dad-11d1-80b4-00c04fd430c8',
      ];

      validUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid)).toBe(true);
      });
    });

    it('should reject invalid UUIDs', () => {
      const invalidUUIDs = [
        '',
        '123',
        '123e4567-e89b-12d3-a456', // too short
        '123e4567-e89b-12d3-a456-************-extra', // too long
        '123e4567_e89b_12d3_a456_************', // wrong separators
        'gggggggg-gggg-gggg-gggg-gggggggggggg', // invalid hex
      ];

      invalidUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid)).toBe(false);
      });
    });
  });

  describe('Schema Validations', () => {
    describe('phoneNumberSchema', () => {
      it('should validate phone numbers with schema', () => {
        const result = phoneNumberSchema.safeParse('+1234567890');
        expect(result.success).toBe(true);
      });

      it('should reject invalid phone numbers with schema', () => {
        const result = phoneNumberSchema.safeParse('invalid');
        expect(result.success).toBe(false);
        expect(result.error?.errors[0].message).toBe('Invalid phone number format');
      });
    });

    describe('emailSchema', () => {
      it('should validate emails with schema', () => {
        const result = emailSchema.safeParse('<EMAIL>');
        expect(result.success).toBe(true);
      });

      it('should reject invalid emails with schema', () => {
        const result = emailSchema.safeParse('invalid-email');
        expect(result.success).toBe(false);
        expect(result.error?.errors[0].message).toBe('Invalid email format');
      });

      it('should reject emails that are too long', () => {
        const longEmail = 'a'.repeat(250) + '@example.com';
        const result = emailSchema.safeParse(longEmail);
        expect(result.success).toBe(false);
        expect(result.error?.errors[0].message).toBe('Email too long');
      });
    });

    describe('nameSchema', () => {
      it('should validate correct names', () => {
        const validNames = [
          'John Doe',
          'Mary-Jane Smith',
          "O'Connor",
          'Jean-Pierre',
          'Smith Jr.',
        ];

        validNames.forEach(name => {
          const result = nameSchema.safeParse(name);
          expect(result.success).toBe(true);
        });
      });

      it('should reject invalid names', () => {
        const invalidNames = [
          '',
          'John123',
          'John@Doe',
          'John_Doe',
          'a'.repeat(101), // too long
        ];

        invalidNames.forEach(name => {
          const result = nameSchema.safeParse(name);
          expect(result.success).toBe(false);
        });
      });
    });

    describe('amountSchema', () => {
      it('should validate positive amounts', () => {
        const validAmounts = [0.01, 1, 99.99, 1000, 999999.99];

        validAmounts.forEach(amount => {
          const result = amountSchema.safeParse(amount);
          expect(result.success).toBe(true);
        });
      });

      it('should reject invalid amounts', () => {
        const invalidAmounts = [0, -1, -99.99, 1000000]; // zero, negative, too large

        invalidAmounts.forEach(amount => {
          const result = amountSchema.safeParse(amount);
          expect(result.success).toBe(false);
        });
      });
    });

    describe('currencySchema', () => {
      it('should validate correct currency codes', () => {
        const validCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD'];

        validCurrencies.forEach(currency => {
          const result = currencySchema.safeParse(currency);
          expect(result.success).toBe(true);
        });
      });

      it('should reject invalid currency codes', () => {
        const invalidCurrencies = [
          '',
          'US', // too short
          'USDD', // too long
          'usd', // lowercase
          '123', // numbers
          'US$', // special characters
        ];

        invalidCurrencies.forEach(currency => {
          const result = currencySchema.safeParse(currency);
          expect(result.success).toBe(false);
        });
      });
    });

    describe('customerRegistrationSchema', () => {
      it('should validate complete registration data', () => {
        const validData = {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
        };

        const result = customerRegistrationSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should reject incomplete registration data', () => {
        const incompleteData = {
          name: 'John Doe',
          // missing email and phone
        };

        const result = customerRegistrationSchema.safeParse(incompleteData);
        expect(result.success).toBe(false);
      });

      it('should reject registration data with invalid fields', () => {
        const invalidData = {
          name: 'John123', // invalid name
          email: 'invalid-email', // invalid email
          phone: 'invalid-phone', // invalid phone
        };

        const result = customerRegistrationSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        expect(result.error?.errors.length).toBeGreaterThan(0);
      });
    });

    describe('paymentRequestSchema', () => {
      it('should validate complete payment request', () => {
        const validData = {
          amount: 99.99,
          currency: 'USD',
          productName: 'Premium Plan',
          productDescription: 'Advanced features',
          customerEmail: '<EMAIL>',
          customerPhone: '+1234567890',
        };

        const result = paymentRequestSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should validate payment request without optional fields', () => {
        const validData = {
          amount: 99.99,
          currency: 'USD',
          productName: 'Basic Plan',
          customerEmail: '<EMAIL>',
          customerPhone: '+1234567890',
          // productDescription is optional
        };

        const result = paymentRequestSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should reject payment request with invalid fields', () => {
        const invalidData = {
          amount: -99.99, // negative amount
          currency: 'invalid', // invalid currency
          productName: '', // empty product name
          customerEmail: 'invalid-email', // invalid email
          customerPhone: 'invalid-phone', // invalid phone
        };

        const result = paymentRequestSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        expect(result.error?.errors.length).toBeGreaterThan(0);
      });

      it('should reject payment request with missing required fields', () => {
        const incompleteData = {
          amount: 99.99,
          // missing other required fields
        };

        const result = paymentRequestSchema.safeParse(incompleteData);
        expect(result.success).toBe(false);
      });
    });
  });
});
