import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SecurityMiddleware, type SecurityConfig } from '@/shared/middleware/Security';
import { createMockLogger } from '../../../mocks/external-apis';
import type { Logger } from '@/shared/utils/logger';

describe('SecurityMiddleware', () => {
  let securityMiddleware: SecurityMiddleware;
  let mockLogger: ReturnType<typeof createMockLogger>;
  let mockConfig: SecurityConfig;

  beforeEach(() => {
    mockLogger = createMockLogger();
    mockConfig = {
      enableCors: true,
      allowedOrigins: ['https://example.com', 'https://app.example.com'],
      enableCSP: true,
      enableHSTS: true,
      maxRequestSize: 1024 * 1024, // 1MB
    };

    securityMiddleware = new SecurityMiddleware(
      mockLogger as unknown as Logger,
      mockConfig
    );
  });

  describe('csp', () => {
    it('should add CSP headers when enabled', async () => {
      const mockContext = {
        header: vi.fn(),
      };
      const next = vi.fn();

      const middleware = securityMiddleware.csp();
      await middleware(mockContext as any, next);

      expect(mockContext.header).toHaveBeenCalledWith('Content-Security-Policy', expect.stringContaining("default-src 'self'"));
      expect(next).toHaveBeenCalled();
    });

    it('should not add CSP headers when disabled', async () => {
      const disabledConfig = { ...mockConfig, enableCSP: false };
      const disabledMiddleware = new SecurityMiddleware(mockLogger as unknown as Logger, disabledConfig);

      const mockContext = {
        header: vi.fn(),
      };
      const next = vi.fn();

      const middleware = disabledMiddleware.csp();
      await middleware(mockContext as any, next);

      expect(mockContext.header).not.toHaveBeenCalled();
      expect(next).toHaveBeenCalled();
    });
  });

  describe('hsts', () => {
    it('should add HSTS headers when enabled', async () => {
      const mockContext = {
        header: vi.fn(),
      };
      const next = vi.fn();

      const middleware = securityMiddleware.hsts();
      await middleware(mockContext as any, next);

      expect(mockContext.header).toHaveBeenCalledWith('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      expect(next).toHaveBeenCalled();
    });

    it('should not add HSTS headers when disabled', async () => {
      const disabledConfig = { ...mockConfig, enableHSTS: false };
      const disabledMiddleware = new SecurityMiddleware(mockLogger as unknown as Logger, disabledConfig);

      const mockContext = {
        header: vi.fn(),
      };
      const next = vi.fn();

      const middleware = disabledMiddleware.hsts();
      await middleware(mockContext as any, next);

      expect(mockContext.header).not.toHaveBeenCalled();
      expect(next).toHaveBeenCalled();
    });
  });

  describe('securityHeaders', () => {
    it('should add security headers', async () => {
      const mockContext = {
        header: vi.fn(),
      };
      const next = vi.fn();

      const middleware = securityMiddleware.securityHeaders();
      await middleware(mockContext as any, next);

      expect(mockContext.header).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');
      expect(mockContext.header).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
      expect(mockContext.header).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');
      expect(mockContext.header).toHaveBeenCalledWith('Referrer-Policy', 'strict-origin-when-cross-origin');
      expect(mockContext.header).toHaveBeenCalledWith('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=()');
      expect(next).toHaveBeenCalled();
    });
  });

  describe('requestSizeLimit', () => {
    it('should allow requests within size limit', async () => {
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue('512'), // 512 bytes
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.requestSizeLimit();
      await middleware(mockContext as any, next);

      expect(next).toHaveBeenCalled();
    });

    it('should reject requests exceeding size limit', async () => {
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue('2097152'), // 2MB
          url: 'https://example.com/test',
        },
        text: vi.fn().mockReturnValue('Request entity too large'),
      };
      const next = vi.fn();

      const middleware = securityMiddleware.requestSizeLimit();
      const result = await middleware(mockContext as any, next);

      expect(mockContext.text).toHaveBeenCalledWith('Request entity too large', 413);
      expect(next).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Request size limit exceeded',
        expect.objectContaining({
          size: 2097152,
          limit: 1048576,
        })
      );
    });

    it('should allow requests without content-length header', async () => {
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue(null),
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.requestSizeLimit();
      await middleware(mockContext as any, next);

      expect(next).toHaveBeenCalled();
    });
  });

  describe('apiKeyAuth', () => {
    it('should allow valid API key', async () => {
      const validApiKeys = ['valid-key-123', 'another-valid-key'];
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue('valid-key-123'),
          query: vi.fn(),
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.apiKeyAuth(validApiKeys);
      await middleware(mockContext as any, next);

      expect(next).toHaveBeenCalled();
    });

    it('should reject invalid API key', async () => {
      const validApiKeys = ['valid-key-123'];
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue('invalid-key'),
          query: vi.fn(),
          url: 'https://example.com/test',
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.apiKeyAuth(validApiKeys);

      await expect(middleware(mockContext as any, next)).rejects.toThrow('Invalid API key');
      expect(next).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Invalid API key used',
        expect.objectContaining({
          apiKey: 'invalid-...',
        })
      );
    });

    it('should reject missing API key', async () => {
      const validApiKeys = ['valid-key-123'];
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue(null),
          query: vi.fn().mockReturnValue(null),
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.apiKeyAuth(validApiKeys);

      await expect(middleware(mockContext as any, next)).rejects.toThrow('API key required');
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('webhookSignatureVerification', () => {
    it('should allow requests with valid signature header', async () => {
      const secret = 'test_secret';
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue('sha256=valid_signature_hash'),
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.webhookSignatureVerification(secret);
      await middleware(mockContext as any, next);

      expect(next).toHaveBeenCalled();
    });

    it('should reject requests without signature header', async () => {
      const secret = 'test_secret';
      const mockContext = {
        req: {
          header: vi.fn().mockReturnValue(null),
          url: 'https://example.com/webhook',
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.webhookSignatureVerification(secret);

      await expect(middleware(mockContext as any, next)).rejects.toThrow('Missing signature');
      expect(next).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Missing webhook signature',
        expect.objectContaining({
          action: 'webhook_signature_verification',
        })
      );
    });

    it('should use custom header name', async () => {
      const secret = 'test_secret';
      const customHeader = 'x-custom-signature';
      const mockContext = {
        req: {
          header: vi.fn().mockImplementation((headerName) => {
            if (headerName === customHeader) return 'sha256=valid_signature_hash';
            return null;
          }),
        },
      };
      const next = vi.fn();

      const middleware = securityMiddleware.webhookSignatureVerification(secret, customHeader);
      await middleware(mockContext as any, next);

      expect(mockContext.req.header).toHaveBeenCalledWith(customHeader);
      expect(next).toHaveBeenCalled();
    });
  });
});
