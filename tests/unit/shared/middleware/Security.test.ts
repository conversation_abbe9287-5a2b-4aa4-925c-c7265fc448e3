import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SecurityMiddleware } from '@/shared/middleware/Security';
import { createMockLogger } from '../../../mocks/external-apis';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';

describe('SecurityMiddleware', () => {
  let securityMiddleware: SecurityMiddleware;
  let mockLogger: ReturnType<typeof createMockLogger>;
  let mockConfig: AppConfig;

  beforeEach(() => {
    mockLogger = createMockLogger();
    mockConfig = {
      security: {
        allowedOrigins: ['https://example.com', 'https://app.example.com'],
        maxRequestSize: 1024 * 1024, // 1MB
        rateLimitEnabled: true,
      },
    } as AppConfig;

    securityMiddleware = new SecurityMiddleware(
      mockLogger as unknown as Logger,
      mockConfig
    );
  });

  describe('corsHeaders', () => {
    it('should add CORS headers for allowed origin', async () => {
      const mockRequest = {
        headers: {
          get: vi.fn().mockReturnValue('https://example.com'),
        },
      };

      const mockResponse = {
        headers: {
          set: vi.fn(),
        },
      };

      const next = vi.fn().mockResolvedValue(mockResponse);

      await securityMiddleware.corsHeaders(mockRequest as any, next);

      expect(mockResponse.headers.set).toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://example.com');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Hub-Signature-256');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Access-Control-Max-Age', '86400');
    });

    it('should not add CORS headers for disallowed origin', async () => {
      const mockRequest = {
        headers: {
          get: vi.fn().mockReturnValue('https://malicious.com'),
        },
      };

      const mockResponse = {
        headers: {
          set: vi.fn(),
        },
      };

      const next = vi.fn().mockResolvedValue(mockResponse);

      await securityMiddleware.corsHeaders(mockRequest as any, next);

      expect(mockResponse.headers.set).not.toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://malicious.com');
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CORS request from disallowed origin',
        expect.objectContaining({
          origin: 'https://malicious.com',
        })
      );
    });

    it('should handle missing origin header', async () => {
      const mockRequest = {
        headers: {
          get: vi.fn().mockReturnValue(null),
        },
      };

      const mockResponse = {
        headers: {
          set: vi.fn(),
        },
      };

      const next = vi.fn().mockResolvedValue(mockResponse);

      await securityMiddleware.corsHeaders(mockRequest as any, next);

      expect(mockResponse.headers.set).not.toHaveBeenCalledWith(
        expect.stringContaining('Access-Control-Allow-Origin'),
        expect.anything()
      );
    });
  });

  describe('securityHeaders', () => {
    it('should add security headers', async () => {
      const mockResponse = {
        headers: {
          set: vi.fn(),
        },
      };

      const next = vi.fn().mockResolvedValue(mockResponse);

      await securityMiddleware.securityHeaders({} as any, next);

      expect(mockResponse.headers.set).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Referrer-Policy', 'strict-origin-when-cross-origin');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'"
      );
    });
  });

  describe('validateContentType', () => {
    it('should allow valid JSON content type', async () => {
      const mockRequest = {
        method: 'POST',
        headers: {
          get: vi.fn().mockReturnValue('application/json'),
        },
      };

      const next = vi.fn().mockResolvedValue({ status: 200 });

      const result = await securityMiddleware.validateContentType(mockRequest as any, next);

      expect(result.status).toBe(200);
      expect(next).toHaveBeenCalled();
    });

    it('should allow valid form content type', async () => {
      const mockRequest = {
        method: 'POST',
        headers: {
          get: vi.fn().mockReturnValue('application/x-www-form-urlencoded'),
        },
      };

      const next = vi.fn().mockResolvedValue({ status: 200 });

      const result = await securityMiddleware.validateContentType(mockRequest as any, next);

      expect(result.status).toBe(200);
      expect(next).toHaveBeenCalled();
    });

    it('should reject invalid content type for POST', async () => {
      const mockRequest = {
        method: 'POST',
        headers: {
          get: vi.fn().mockReturnValue('text/plain'),
        },
      };

      const next = vi.fn();

      const result = await securityMiddleware.validateContentType(mockRequest as any, next);

      expect(result.status).toBe(415);
      expect(result.body).toEqual({ error: 'Unsupported content type' });
      expect(next).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Invalid content type',
        expect.objectContaining({
          contentType: 'text/plain',
          method: 'POST',
        })
      );
    });

    it('should allow GET requests without content type validation', async () => {
      const mockRequest = {
        method: 'GET',
        headers: {
          get: vi.fn().mockReturnValue(null),
        },
      };

      const next = vi.fn().mockResolvedValue({ status: 200 });

      const result = await securityMiddleware.validateContentType(mockRequest as any, next);

      expect(result.status).toBe(200);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('validateRequestSize', () => {
    it('should allow requests within size limit', async () => {
      const mockRequest = {
        headers: {
          get: vi.fn().mockReturnValue('512'), // 512 bytes
        },
      };

      const next = vi.fn().mockResolvedValue({ status: 200 });

      const result = await securityMiddleware.validateRequestSize(mockRequest as any, next);

      expect(result.status).toBe(200);
      expect(next).toHaveBeenCalled();
    });

    it('should reject requests exceeding size limit', async () => {
      const mockRequest = {
        headers: {
          get: vi.fn().mockReturnValue('2097152'), // 2MB
        },
      };

      const next = vi.fn();

      const result = await securityMiddleware.validateRequestSize(mockRequest as any, next);

      expect(result.status).toBe(413);
      expect(result.body).toEqual({ error: 'Request too large' });
      expect(next).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Request size exceeds limit',
        expect.objectContaining({
          size: 2097152,
          limit: 1048576,
        })
      );
    });

    it('should allow requests without content-length header', async () => {
      const mockRequest = {
        headers: {
          get: vi.fn().mockReturnValue(null),
        },
      };

      const next = vi.fn().mockResolvedValue({ status: 200 });

      const result = await securityMiddleware.validateRequestSize(mockRequest as any, next);

      expect(result.status).toBe(200);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('sanitizeInput', () => {
    it('should sanitize malicious input', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const sanitized = securityMiddleware.sanitizeInput(maliciousInput);

      expect(sanitized).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;Hello World');
    });

    it('should handle null input', () => {
      const sanitized = securityMiddleware.sanitizeInput(null);
      expect(sanitized).toBe('');
    });

    it('should handle undefined input', () => {
      const sanitized = securityMiddleware.sanitizeInput(undefined);
      expect(sanitized).toBe('');
    });

    it('should handle non-string input', () => {
      const sanitized = securityMiddleware.sanitizeInput(123 as any);
      expect(sanitized).toBe('123');
    });

    it('should preserve safe content', () => {
      const safeInput = 'Hello World! This is safe content.';
      const sanitized = securityMiddleware.sanitizeInput(safeInput);
      expect(sanitized).toBe(safeInput);
    });
  });

  describe('validateSignature', () => {
    it('should validate correct HMAC signature', () => {
      const payload = '{"test": "data"}';
      const secret = 'test_secret';
      const validSignature = 'sha256=valid_signature_hash';

      // Mock the crypto verification
      const isValid = securityMiddleware.validateSignature(payload, validSignature, secret);

      // This would need actual HMAC implementation to test properly
      expect(typeof isValid).toBe('boolean');
    });

    it('should reject invalid signature format', () => {
      const payload = '{"test": "data"}';
      const secret = 'test_secret';
      const invalidSignature = 'invalid_format';

      const isValid = securityMiddleware.validateSignature(payload, invalidSignature, secret);

      expect(isValid).toBe(false);
    });

    it('should reject missing signature', () => {
      const payload = '{"test": "data"}';
      const secret = 'test_secret';

      const isValid = securityMiddleware.validateSignature(payload, '', secret);

      expect(isValid).toBe(false);
    });
  });
});
