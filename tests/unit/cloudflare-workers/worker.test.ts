import { describe, it, expect, beforeEach } from 'vitest';
import { env, createExecutionContext, waitOnExecutionContext } from 'cloudflare:test';
import worker from '@/index';

// For correctly-typed Request to pass to worker.fetch()
const IncomingRequest = Request<unknown, IncomingRequestCfProperties>;

describe('WhatsApp Flow Worker (Unit Tests)', () => {
  beforeEach(async () => {
    // Clean up test data before each test
    await env.TEST_DB.exec(`
      DELETE FROM rate_limits;
      DELETE FROM audit_logs;
      DELETE FROM flow_sessions;
      DELETE FROM payments;
      DELETE FROM users;
    `);
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const request = new IncomingRequest('https://example.com/health');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toEqual({
        status: 'healthy',
        service: 'whatsapp-flows-hono',
        timestamp: expect.any(String),
        version: '1.0.0',
      });
    });
  });

  describe('WhatsApp Webhook Verification', () => {
    it('should verify webhook with correct parameters', async () => {
      const verifyToken = env.WHATSAPP_VERIFY_TOKEN;
      const challenge = 'test_challenge_123';
      
      const request = new IncomingRequest(
        `https://example.com/webhook/whatsapp?hub.mode=subscribe&hub.verify_token=${verifyToken}&hub.challenge=${challenge}`
      );
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      expect(await response.text()).toBe(challenge);
    });

    it('should reject webhook with wrong token', async () => {
      const wrongToken = 'wrong_token';
      const challenge = 'test_challenge_123';
      
      const request = new IncomingRequest(
        `https://example.com/webhook/whatsapp?hub.mode=subscribe&hub.verify_token=${wrongToken}&hub.challenge=${challenge}`
      );
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(403);
    });

    it('should handle missing parameters', async () => {
      const request = new IncomingRequest('https://example.com/webhook/whatsapp');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(400);
    });
  });

  describe('DPO Payment Callbacks', () => {
    it('should handle successful payment callback', async () => {
      // First create a payment record
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+**********',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'pending',
        'Premium Plan',
        '<EMAIL>',
        '+**********',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const callbackData = {
        TransactionToken: 'DPO_TOKEN_123',
        CompanyRef: 'payment-123',
        TransID: 'DPO_TRANS_123',
        CCDapproval: '123456',
        Result: '000',
        ResultExplanation: 'Transaction Paid',
      };

      const request = new IncomingRequest('https://example.com/webhook/dpo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(callbackData).toString(),
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      
      // Verify payment was updated
      const payment = await env.TEST_DB.prepare('SELECT * FROM payments WHERE id = ?')
        .bind('payment-123')
        .first();
      
      expect(payment?.status).toBe('completed');
      expect(payment?.dpo_payment_reference).toBe('DPO_TRANS_123');
    });

    it('should handle failed payment callback', async () => {
      // Create payment record
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+**********',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'pending',
        'Premium Plan',
        '<EMAIL>',
        '+**********',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const callbackData = {
        TransactionToken: 'DPO_TOKEN_123',
        CompanyRef: 'payment-123',
        Result: '901',
        ResultExplanation: 'Transaction Declined',
      };

      const request = new IncomingRequest('https://example.com/webhook/dpo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(callbackData).toString(),
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      
      // Verify payment was marked as failed
      const payment = await env.TEST_DB.prepare('SELECT * FROM payments WHERE id = ?')
        .bind('payment-123')
        .first();
      
      expect(payment?.status).toBe('failed');
      expect(payment?.failure_reason).toBe('Transaction Declined');
    });
  });

  describe('Payment Status Pages', () => {
    it('should show success page for completed payment', async () => {
      // Create completed payment
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+**********',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at, completed_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'completed',
        'Premium Plan',
        '<EMAIL>',
        '+**********',
        new Date().toISOString(),
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const request = new IncomingRequest('https://example.com/payments/success?payment_id=payment-123');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toContain('text/html');
      
      const html = await response.text();
      expect(html).toContain('Payment Successful');
      expect(html).toContain('Premium Plan');
      expect(html).toContain('$99.99');
    });

    it('should show error page for failed payment', async () => {
      // Create failed payment
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+**********',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at, failed_at, failure_reason)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'failed',
        'Premium Plan',
        '<EMAIL>',
        '+**********',
        new Date().toISOString(),
        new Date().toISOString(),
        new Date().toISOString(),
        'Payment declined by bank'
      ).run();

      const request = new IncomingRequest('https://example.com/payments/error?payment_id=payment-123');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toContain('text/html');
      
      const html = await response.text();
      expect(html).toContain('Payment Failed');
      expect(html).toContain('Payment declined by bank');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown routes', async () => {
      const request = new IncomingRequest('https://example.com/unknown-route');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(404);
    });

    it('should handle method not allowed', async () => {
      const request = new IncomingRequest('https://example.com/api/whatsapp/webhook', {
        method: 'PUT',
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(405);
    });

    it('should handle internal server errors gracefully', async () => {
      // This would test error handling when services throw unexpected errors
      const request = new IncomingRequest('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Malformed payload that might cause processing errors
          object: 'whatsapp_business_account',
          entry: [
            {
              id: null, // Invalid data
              changes: null, // Invalid data
            },
          ],
        }),
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      // Should handle errors gracefully
      expect([200, 400, 500]).toContain(response.status);
    });
  });
});
