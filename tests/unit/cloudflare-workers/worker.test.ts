import { describe, it, expect, beforeEach, vi } from 'vitest';
import { env, createExecutionContext, waitOnExecutionContext } from 'cloudflare:test';
import worker from '@/index';

// For correctly-typed Request to pass to worker.fetch()
const IncomingRequest = Request<unknown, IncomingRequestCfProperties>;

describe('WhatsApp Flow Worker (Unit Tests)', () => {
  beforeEach(() => {
    // Mock fetch for external API calls
    global.fetch = vi.fn().mockImplementation((url: string, options?: any) => {
      // Mock DPO API responses
      if (url.includes('secure.3gdirectpay.com/payv2.php')) {
        const body = JSON.parse(options?.body || '{}');

        if (body.Request === 'verifyToken') {
          // Different responses based on transaction token
          if (body.TransactionToken === 'DPO_TOKEN_123') {
            return Promise.resolve({
              ok: true,
              json: () => Promise.resolve({
                Result: '000',
                ResultExplanation: 'Transaction verified successfully',
                TransactionStatus: '1', // Completed
                TransactionAmount: '99.99',
                TransactionCurrency: 'USD',
                CustomerEmail: '<EMAIL>',
                CustomerPhone: '+1234567890',
                TransactionRef: 'DPO_TRANS_123',
              }),
            });
          }

          if (body.TransactionToken === 'DPO_TOKEN_FAILED') {
            return Promise.resolve({
              ok: true,
              json: () => Promise.resolve({
                Result: '000',
                ResultExplanation: 'Transaction verified successfully',
                TransactionStatus: '2', // Failed
                TransactionAmount: '99.99',
                TransactionCurrency: 'USD',
                CustomerEmail: '<EMAIL>',
                CustomerPhone: '+1234567890',
                TransactionRef: 'DPO_TRANS_FAILED',
              }),
            });
          }
        }

        if (body.Request === 'cancelToken') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              Result: '000',
              ResultExplanation: 'Transaction cancelled successfully',
            }),
          });
        }
      }

      // Default mock response
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      });
    });
  });
  beforeEach(async () => {
    // Clean up test data before each test
    await env.TEST_DB.exec(`
      DELETE FROM rate_limits;
      DELETE FROM audit_logs;
      DELETE FROM flow_sessions;
      DELETE FROM payments;
      DELETE FROM users;
    `);
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const request = new IncomingRequest('https://example.com/health');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toEqual({
        status: 'healthy',
        service: 'whatsapp-flows-hono',
        timestamp: expect.any(String),
      });
    });
  });

  describe('WhatsApp Webhook Verification', () => {
    it('should verify webhook with correct parameters', async () => {
      const verifyToken = env.WHATSAPP_VERIFY_TOKEN;
      const challenge = 'test_challenge_123';
      
      const request = new IncomingRequest(
        `https://example.com/webhook/whatsapp?hub.mode=subscribe&hub.verify_token=${verifyToken}&hub.challenge=${challenge}`
      );
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      expect(await response.text()).toBe(challenge);
    });

    it('should reject webhook with wrong token', async () => {
      const wrongToken = 'wrong_token';
      const challenge = 'test_challenge_123';
      
      const request = new IncomingRequest(
        `https://example.com/webhook/whatsapp?hub.mode=subscribe&hub.verify_token=${wrongToken}&hub.challenge=${challenge}`
      );
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(403);
    });

    it('should handle missing parameters', async () => {
      const request = new IncomingRequest('https://example.com/webhook/whatsapp');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(403);
    });
  });

  describe('DPO Payment Callbacks', () => {
    it('should handle successful payment callback', async () => {
      // First create a payment record
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+1234567890',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'pending',
        'Premium Plan',
        '<EMAIL>',
        '+1234567890',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const callbackData = {
        TransactionToken: 'DPO_TOKEN_123',
        CompanyRef: 'payment-123',
        TransID: 'DPO_TRANS_123',
        CCDapproval: '123456',
        Result: '000',
        ResultExplanation: 'Transaction Paid',
      };

      const request = new IncomingRequest('https://example.com/webhook/dpo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(callbackData).toString(),
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      
      // Verify payment was updated
      const payment = await env.TEST_DB.prepare('SELECT * FROM payments WHERE id = ?')
        .bind('payment-123')
        .first();
      
      expect(payment?.status).toBe('completed');
      expect(payment?.dpo_payment_reference).toBe('DPO_TRANS_123');
    });

    it('should handle failed payment callback', async () => {
      // Create payment record
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+1234567890',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_FAILED',
        99.99,
        'USD',
        'pending',
        'Premium Plan',
        '<EMAIL>',
        '+1234567890',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const callbackData = {
        TransactionToken: 'DPO_TOKEN_FAILED',
        CompanyRef: 'payment-123',
        Result: '901',
        ResultExplanation: 'Transaction Declined',
      };

      const request = new IncomingRequest('https://example.com/webhook/dpo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(callbackData).toString(),
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      
      // Verify payment was marked as failed
      const payment = await env.TEST_DB.prepare('SELECT * FROM payments WHERE id = ?')
        .bind('payment-123')
        .first();
      
      expect(payment?.status).toBe('failed');
      expect(payment?.failure_reason).toBe('Transaction Declined');
    });
  });

  describe('Payment Status Pages', () => {
    it('should show success page for completed payment', async () => {
      // Create completed payment
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+1234567890',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at, completed_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'completed',
        'Premium Plan',
        '<EMAIL>',
        '+1234567890',
        new Date().toISOString(),
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const request = new IncomingRequest('https://example.com/payment/callback?TransactionToken=DPO_TOKEN_123&CompanyRef=payment-123');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toContain('text/html');
      
      const html = await response.text();
      expect(html).toContain('Payment Successful');
      expect(html).toContain('Premium Plan');
      expect(html).toContain('$99.99');
    });

    it('should show cancellation page for cancelled payment', async () => {
      // Create payment that can be cancelled
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+1234567890',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'payment-123',
        'user-123',
        'DPO_TOKEN_123',
        99.99,
        'USD',
        'pending',
        'Premium Plan',
        '<EMAIL>',
        '+1234567890',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const request = new IncomingRequest('https://example.com/payment/cancel?TransactionToken=DPO_TOKEN_123');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toContain('text/html');
      
      const html = await response.text();
      expect(html).toContain('Payment Cancelled');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown routes', async () => {
      const request = new IncomingRequest('https://example.com/unknown-route');
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(404);
    });

    it('should handle method not allowed', async () => {
      const request = new IncomingRequest('https://example.com/webhook/whatsapp', {
        method: 'PUT',
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      expect(response.status).toBe(404); // Hono returns 404 for unsupported methods
    });

    it('should handle internal server errors gracefully', async () => {
      // This would test error handling when services throw unexpected errors
      const request = new IncomingRequest('https://example.com/webhook/whatsapp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Malformed payload that might cause processing errors
          object: 'whatsapp_business_account',
          entry: [
            {
              id: null, // Invalid data
              changes: null, // Invalid data
            },
          ],
        }),
      });
      const ctx = createExecutionContext();
      
      const response = await worker.fetch(request, env, ctx);
      await waitOnExecutionContext(ctx);
      
      // Should handle errors gracefully (401 for missing signature is expected)
      expect([200, 400, 401, 500]).toContain(response.status);
    });
  });
});
