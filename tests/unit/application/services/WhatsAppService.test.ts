import { describe, it, expect, beforeEach } from 'vitest';
import { WhatsAppService } from '@/application/services/WhatsAppService';
import {
  createMockWhatsAppAdapter,
  createMockUserService,
  createMockFlowService,
  createMockLogger
} from '../../../mocks/external-apis';
import type { WhatsAppAdapter } from '@/infrastructure/adapters/WhatsAppAdapter';
import type { UserService } from '@/application/services/UserService';
import type { FlowService } from '@/application/services/FlowService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';
import type { WhatsAppMessage, WhatsAppMetadata } from '@/shared/types/whatsapp';

describe('WhatsAppService', () => {
  let whatsAppService: WhatsAppService;
  let mockWhatsAppAdapter: ReturnType<typeof createMockWhatsAppAdapter>;
  let mockFlowService: ReturnType<typeof createMockFlowService>;
  let mockUserService: ReturnType<typeof createMockUserService>;
  let mockLogger: ReturnType<typeof createMockLogger>;
  let mockConfig: AppConfig;

  beforeEach(() => {
    mockWhatsAppAdapter = createMockWhatsAppAdapter();
    mockFlowService = createMockFlowService();
    mockUserService = createMockUserService();
    mockLogger = createMockLogger();

    mockConfig = {
      flows: {
        customerRegistrationFlowId: 'reg-flow-123',
        paymentFlowId: 'payment-flow-123',
      },
    } as AppConfig;

    whatsAppService = new WhatsAppService(
      mockWhatsAppAdapter as unknown as WhatsAppAdapter,
      mockFlowService as unknown as FlowService,
      mockUserService as unknown as UserService,
      mockLogger as unknown as Logger,
      mockConfig
    );
  });

  describe('handleIncomingMessage', () => {
    it('should handle "hello" message for new user', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        text: { body: 'hello' },
        type: 'text',
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(null);
      mockUserService.createUser.mockResolvedValue({
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
      });
      mockWhatsAppAdapter.sendInteractiveMessage.mockResolvedValue({ messageId: 'reply-123' });

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockUserService.createUser).toHaveBeenCalledWith({
        whatsappPhoneNumber: '+1234567890',
      });
      expect(mockWhatsAppAdapter.markMessageAsRead).toHaveBeenCalledWith('msg-123');
      expect(mockWhatsAppAdapter.sendInteractiveMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '+1234567890',
          type: 'button',
          body: expect.objectContaining({
            text: expect.stringContaining('Welcome'),
          }),
        })
      );
    });

    it('should handle "hello" message for existing user', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        text: { body: 'hello' },
        type: 'text',
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      const existingUser = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        name: 'John Doe',
        registrationStatus: 'completed',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(existingUser);
      mockWhatsAppAdapter.sendInteractiveMessage.mockResolvedValue({ messageId: 'reply-123' });

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockUserService.createUser).not.toHaveBeenCalled();
      expect(mockWhatsAppAdapter.sendInteractiveMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '+1234567890',
          type: 'button',
          body: expect.objectContaining({
            text: expect.stringContaining('Welcome'),
          }),
        })
      );
    });

    it('should handle "register" command', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        text: { body: 'register' },
        type: 'text',
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      const user = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(user);
      mockFlowService.startCustomerRegistrationFlow.mockResolvedValue({
        id: 'session-123',
        flowId: 'reg-flow-123',
      });
      mockWhatsAppAdapter.sendFlowMessage.mockResolvedValue({ messageId: 'flow-123' });

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockFlowService.startCustomerRegistrationFlow).toHaveBeenCalledWith(user.id);
      expect(mockWhatsAppAdapter.sendFlowMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '+1234567890',
          flowId: 'reg-flow-123',
        })
      );
    });

    it('should handle "pay" command', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        text: { body: 'pay' },
        type: 'text',
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      const user = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
        registrationStatus: 'completed',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(user);
      mockUserService.getUserById.mockResolvedValue(user);
      mockFlowService.startPaymentFlow.mockResolvedValue({
        id: 'session-123',
        flowId: 'payment-flow-123',
      });
      mockWhatsAppAdapter.sendFlowMessage.mockResolvedValue({ messageId: 'flow-123' });

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockFlowService.startPaymentFlow).toHaveBeenCalledWith(user.id);
      expect(mockWhatsAppAdapter.sendFlowMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '+1234567890',
          flowId: 'payment-flow-123',
        })
      );
    });

    it('should handle unknown commands', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        text: { body: 'unknown command' },
        type: 'text',
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      const user = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(user);
      mockWhatsAppAdapter.sendTextMessage.mockResolvedValue({ messageId: 'reply-123' });

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockWhatsAppAdapter.sendTextMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '+1234567890',
          text: expect.stringContaining('didn\'t understand'),
        })
      );
    });

    it('should handle service errors gracefully', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        text: { body: 'hello' },
        type: 'text',
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      mockUserService.getUserByWhatsAppPhone.mockRejectedValue(new Error('Database error'));

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling incoming message',
        expect.objectContaining({
          messageId: 'msg-123',
          from: '+1234567890',
          error: 'Database error',
        })
      );
    });

    it('should handle interactive message with flow response', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        type: 'interactive',
        interactive: {
          type: 'nfm_reply',
          nfm_reply: {
            response_json: JSON.stringify({
              name: 'John Doe',
              email: '<EMAIL>',
            }),
            body: 'Registration completed',
            name: 'flow_response',
          },
        },
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      const user = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(user);
      mockFlowService.processFlowResponse.mockResolvedValue(undefined);

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockFlowService.processFlowResponse).toHaveBeenCalledWith(
        user.id,
        {
          name: 'John Doe',
          email: '<EMAIL>',
        }
      );
    });

    it('should handle button reply message', async () => {
      const message: WhatsAppMessage = {
        from: '+1234567890',
        id: 'msg-123',
        timestamp: '1699564800',
        type: 'interactive',
        interactive: {
          type: 'button_reply',
          button_reply: {
            id: 'start_registration',
            title: 'Register',
          },
        },
      };

      const metadata: WhatsAppMetadata = {
        display_phone_number: '1234567890',
        phone_number_id: 'test-phone-id',
      };

      const user = {
        id: 'user-123',
        whatsappPhoneNumber: '+1234567890',
      };

      mockUserService.getUserByWhatsAppPhone.mockResolvedValue(user);
      mockFlowService.startCustomerRegistrationFlow.mockResolvedValue({
        id: 'session-123',
        flowId: 'reg-flow-123',
      });
      mockWhatsAppAdapter.sendFlowMessage.mockResolvedValue({ messageId: 'flow-123' });

      await whatsAppService.handleIncomingMessage(message, metadata);

      expect(mockFlowService.startCustomerRegistrationFlow).toHaveBeenCalledWith(user.id);
    });
  });

  describe('handleMessageStatus', () => {
    it('should handle message status updates', async () => {
      const status = {
        id: 'msg-123',
        status: 'delivered',
        timestamp: '1699564800',
        recipient_id: '+1234567890',
      };

      await whatsAppService.handleMessageStatus(status);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Handling message status update',
        expect.objectContaining({
          action: 'handle_message_status',
          messageId: status.id,
          status: status.status,
          recipientId: status.recipient_id,
        })
      );
    });
  });
});
