import { describe, it, expect, beforeEach } from 'vitest';
import { PaymentService } from '@/application/services/PaymentService';
import {
  createMockPaymentRepository,
  createMockDpoAdapter,
  createMockLogger
} from '../../../mocks/external-apis';
import type { PaymentRepository } from '@/infrastructure/repositories/PaymentRepository';
import type { DpoAdapter } from '@/infrastructure/adapters/DpoAdapter';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';

describe('PaymentService', () => {
  let paymentService: PaymentService;
  let mockPaymentRepository: ReturnType<typeof createMockPaymentRepository>;
  let mockDpoAdapter: ReturnType<typeof createMockDpoAdapter>;
  let mockLogger: ReturnType<typeof createMockLogger>;
  let mockConfig: AppConfig;

  beforeEach(() => {
    mockPaymentRepository = createMockPaymentRepository();
    mockDpoAdapter = createMockDpoAdapter();
    mockLogger = createMockLogger();
    
    mockConfig = {
      business: {
        paymentTimeoutMinutes: 15,
        sessionTimeoutMinutes: 30,
        defaultCurrency: 'USD',
      },
      app: {
        url: 'https://test.workers.dev',
      },
    } as AppConfig;

    paymentService = new PaymentService(
      mockPaymentRepository as unknown as PaymentRepository,
      mockDpoAdapter as unknown as DpoAdapter,
      mockLogger as unknown as Logger,
      mockConfig
    );
  });

  describe('createPayment', () => {
    it('should create payment successfully', async () => {
      const paymentData = {
        userId: 'user-123',
        amount: 99.99,
        currency: 'USD',
        productName: 'Premium Plan',
        productDescription: 'Advanced features',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      const expectedPayment = {
        id: 'payment-123',
        ...paymentData,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPaymentRepository.create.mockResolvedValue(expectedPayment);

      const result = await paymentService.createPayment(paymentData);

      expect(result).toEqual(expectedPayment);
      expect(mockPaymentRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: paymentData.userId,
          amount: paymentData.amount,
          currency: paymentData.currency,
          productName: paymentData.productName,
          customerEmail: paymentData.customerEmail,
          customerPhone: paymentData.customerPhone,
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Payment created successfully',
        expect.objectContaining({
          paymentId: expectedPayment.id,
          userId: paymentData.userId,
          amount: paymentData.amount,
        })
      );
    });

    it('should throw error for invalid amount', async () => {
      const paymentData = {
        userId: 'user-123',
        amount: -10, // Invalid negative amount
        currency: 'USD',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      await expect(paymentService.createPayment(paymentData)).rejects.toThrow('Invalid payment amount');
      expect(mockPaymentRepository.create).not.toHaveBeenCalled();
    });

    it('should throw error for invalid currency', async () => {
      const paymentData = {
        userId: 'user-123',
        amount: 99.99,
        currency: 'INVALID', // Invalid currency
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      await expect(paymentService.createPayment(paymentData)).rejects.toThrow('Invalid currency');
      expect(mockPaymentRepository.create).not.toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      const paymentData = {
        userId: 'user-123',
        amount: 99.99,
        currency: 'USD',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      mockPaymentRepository.create.mockRejectedValue(new Error('Database error'));

      await expect(paymentService.createPayment(paymentData)).rejects.toThrow('Failed to create payment');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to create payment',
        expect.objectContaining({
          error: 'Database error',
        })
      );
    });
  });

  describe('initiatePayment', () => {
    it('should initiate payment with DPO successfully', async () => {
      const paymentId = 'payment-123';
      const mockPayment = {
        id: paymentId,
        userId: 'user-123',
        amount: 99.99,
        currency: 'USD',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        status: 'pending',
      };

      const mockDpoResponse = {
        success: true,
        reference: 'DPO_REF_123',
        paymentUrl: 'https://secure.3gdirectpay.com/?ID=TOKEN_123',
        transactionToken: 'TOKEN_123',
      };

      const updatedPayment = {
        ...mockPayment,
        dpoTransactionToken: mockDpoResponse.transactionToken,
        dpoPaymentReference: mockDpoResponse.reference,
        paymentUrl: mockDpoResponse.paymentUrl,
      };

      mockPaymentRepository.findById.mockResolvedValue(mockPayment);
      mockDpoAdapter.createPaymentToken.mockResolvedValue(mockDpoResponse);
      mockPaymentRepository.update.mockResolvedValue(updatedPayment);

      const result = await paymentService.initiatePayment(paymentId);

      expect(result.paymentUrl).toBe(mockDpoResponse.paymentUrl);
      expect(result.payment).toEqual(updatedPayment);
      expect(mockDpoAdapter.createPaymentToken).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: mockPayment.amount,
          currency: mockPayment.currency,
          customerEmail: mockPayment.customerEmail,
          customerPhone: mockPayment.customerPhone,
          productName: mockPayment.productName,
        })
      );
      expect(mockPaymentRepository.update).toHaveBeenCalledWith(
        paymentId,
        expect.objectContaining({
          dpoTransactionToken: mockDpoResponse.transactionToken,
          dpoPaymentReference: mockDpoResponse.reference,
          paymentUrl: mockDpoResponse.paymentUrl,
        })
      );
    });

    it('should throw error for non-existent payment', async () => {
      const paymentId = 'non-existent-payment';

      mockPaymentRepository.findById.mockResolvedValue(null);

      await expect(paymentService.initiatePayment(paymentId)).rejects.toThrow('Payment not found');
      expect(mockDpoAdapter.createPaymentToken).not.toHaveBeenCalled();
    });

    it('should handle DPO adapter errors', async () => {
      const paymentId = 'payment-123';
      const mockPayment = {
        id: paymentId,
        userId: 'user-123',
        amount: 99.99,
        currency: 'USD',
        productName: 'Premium Plan',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        status: 'pending',
      };

      mockPaymentRepository.findById.mockResolvedValue(mockPayment);
      mockDpoAdapter.createPaymentToken.mockRejectedValue(new Error('DPO API error'));

      await expect(paymentService.initiatePayment(paymentId)).rejects.toThrow('Failed to initiate payment');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to initiate payment with DPO',
        expect.objectContaining({
          paymentId,
          error: 'DPO API error',
        })
      );
    });
  });

  describe('updatePaymentStatus', () => {
    it('should update payment status successfully', async () => {
      const paymentId = 'payment-123';
      const newStatus = 'completed';
      const updatedPayment = {
        id: paymentId,
        status: newStatus,
        completedAt: new Date(),
      };

      mockPaymentRepository.update.mockResolvedValue(updatedPayment);

      const result = await paymentService.updatePaymentStatus(paymentId, newStatus);

      expect(result).toEqual(updatedPayment);
      expect(mockPaymentRepository.update).toHaveBeenCalledWith(
        paymentId,
        expect.objectContaining({
          status: newStatus,
          completedAt: expect.any(Date),
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Payment status updated',
        expect.objectContaining({
          paymentId,
          status: newStatus,
        })
      );
    });

    it('should handle failed status update', async () => {
      const paymentId = 'payment-123';
      const newStatus = 'failed';
      const failureReason = 'Payment declined';

      const updatedPayment = {
        id: paymentId,
        status: newStatus,
        failedAt: new Date(),
        failureReason,
      };

      mockPaymentRepository.update.mockResolvedValue(updatedPayment);

      const result = await paymentService.updatePaymentStatus(paymentId, newStatus, failureReason);

      expect(result).toEqual(updatedPayment);
      expect(mockPaymentRepository.update).toHaveBeenCalledWith(
        paymentId,
        expect.objectContaining({
          status: newStatus,
          failedAt: expect.any(Date),
          failureReason,
        })
      );
    });

    it('should return null for non-existent payment', async () => {
      const paymentId = 'non-existent-payment';

      mockPaymentRepository.update.mockResolvedValue(null);

      const result = await paymentService.updatePaymentStatus(paymentId, 'completed');

      expect(result).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Payment not found for status update',
        expect.objectContaining({
          paymentId,
        })
      );
    });
  });

  describe('getPaymentById', () => {
    it('should return payment when found', async () => {
      const paymentId = 'payment-123';
      const expectedPayment = {
        id: paymentId,
        userId: 'user-123',
        amount: 99.99,
        currency: 'USD',
        status: 'completed',
      };

      mockPaymentRepository.findById.mockResolvedValue(expectedPayment);

      const result = await paymentService.getPaymentById(paymentId);

      expect(result).toEqual(expectedPayment);
      expect(mockPaymentRepository.findById).toHaveBeenCalledWith(paymentId);
    });

    it('should return null when payment not found', async () => {
      const paymentId = 'non-existent-payment';

      mockPaymentRepository.findById.mockResolvedValue(null);

      const result = await paymentService.getPaymentById(paymentId);

      expect(result).toBeNull();
    });

    it('should handle repository errors gracefully', async () => {
      const paymentId = 'payment-123';

      mockPaymentRepository.findById.mockRejectedValue(new Error('Database error'));

      const result = await paymentService.getPaymentById(paymentId);

      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get payment by ID',
        expect.objectContaining({
          paymentId,
          error: 'Database error',
        })
      );
    });
  });

  describe('getPaymentStats', () => {
    it('should return payment statistics', async () => {
      const expectedStats = {
        total: 100,
        completed: 85,
        pending: 10,
        failed: 5,
        totalAmount: 9999.99,
        completedAmount: 8499.99,
      };

      mockPaymentRepository.getPaymentStats.mockResolvedValue(expectedStats);

      const result = await paymentService.getPaymentStats();

      expect(result).toEqual(expectedStats);
      expect(mockPaymentRepository.getPaymentStats).toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      mockPaymentRepository.getPaymentStats.mockRejectedValue(new Error('Database error'));

      await expect(paymentService.getPaymentStats()).rejects.toThrow('Failed to get payment stats');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get payment stats',
        expect.objectContaining({
          error: 'Database error',
        })
      );
    });
  });

  describe('handlePaymentCallback', () => {
    it('should handle successful payment callback', async () => {
      const dpoReference = 'DPO_REF_123';
      const status = 'completed';
      const mockPayment = {
        id: 'payment-123',
        userId: 'user-123',
        dpoPaymentReference: dpoReference,
        status: 'pending',
      };

      mockPaymentRepository.findByDpoReference.mockResolvedValue(mockPayment);
      mockPaymentRepository.update.mockResolvedValue({
        ...mockPayment,
        status: 'completed',
      });

      const result = await paymentService.handlePaymentCallback(dpoReference, status);

      expect(result?.status).toBe('completed');
      expect(mockPaymentRepository.update).toHaveBeenCalledWith(
        mockPayment.id,
        expect.objectContaining({
          status: 'completed',
        })
      );
    });

    it('should return null for non-existent payment', async () => {
      const dpoReference = 'NON_EXISTENT_REF';
      const status = 'completed';

      mockPaymentRepository.findByDpoReference.mockResolvedValue(null);

      const result = await paymentService.handlePaymentCallback(dpoReference, status);

      expect(result).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Payment not found for DPO reference',
        expect.objectContaining({
          dpoReference,
        })
      );
    });
  });

  describe('getPaymentsByUserId', () => {
    it('should return payments for user', async () => {
      const userId = 'user-123';
      const expectedPayments = [
        { id: 'payment-1', userId, amount: 99.99 },
        { id: 'payment-2', userId, amount: 199.99 },
      ];

      mockPaymentRepository.findByUserId.mockResolvedValue(expectedPayments);

      const result = await paymentService.getPaymentsByUserId(userId);

      expect(result).toEqual(expectedPayments);
      expect(mockPaymentRepository.findByUserId).toHaveBeenCalledWith(userId, 10);
    });

    it('should handle repository errors gracefully', async () => {
      const userId = 'user-123';

      mockPaymentRepository.findByUserId.mockRejectedValue(new Error('Database error'));

      const result = await paymentService.getPaymentsByUserId(userId);

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get payments by user ID',
        expect.objectContaining({
          userId,
          error: 'Database error',
        })
      );
    });
  });

  describe('processExpiredPayments', () => {
    it('should process expired payments', async () => {
      const expiredPayments = [
        { id: 'payment-1', status: 'pending' },
        { id: 'payment-2', status: 'pending' },
      ];

      mockPaymentRepository.findExpiredPayments.mockResolvedValue(expiredPayments);
      mockPaymentRepository.update.mockResolvedValue(null);

      const result = await paymentService.processExpiredPayments();

      expect(result).toBe(2);
      expect(mockPaymentRepository.update).toHaveBeenCalledTimes(2);
      expect(mockPaymentRepository.update).toHaveBeenCalledWith(
        'payment-1',
        expect.objectContaining({
          status: 'expired',
          failureReason: 'Payment timeout exceeded',
        })
      );
    });
  });
});
