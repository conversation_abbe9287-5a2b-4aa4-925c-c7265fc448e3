import { describe, it, expect, beforeEach } from 'vitest';
import { FlowService } from '@/application/services/FlowService';
import {
  createMockFlowSessionRepository,
  createMockUserService,
  createMockPaymentService,
  createMockLogger
} from '../../../mocks/external-apis';
import type { FlowSessionRepository } from '@/infrastructure/repositories/FlowSessionRepository';
import type { UserService } from '@/application/services/UserService';
import type { PaymentService } from '@/application/services/PaymentService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';

describe('FlowService', () => {
  let flowService: FlowService;
  let mockFlowSessionRepository: ReturnType<typeof createMockFlowSessionRepository>;
  let mockUserService: ReturnType<typeof createMockUserService>;
  let mockPaymentService: ReturnType<typeof createMockPaymentService>;
  let mockLogger: ReturnType<typeof createMockLogger>;
  let mockConfig: AppConfig;

  beforeEach(() => {
    mockFlowSessionRepository = createMockFlowSessionRepository();
    mockUserService = createMockUserService();
    mockPaymentService = createMockPaymentService();
    mockLogger = createMockLogger();
    
    mockConfig = {
      business: {
        sessionTimeoutMinutes: 30,
      },
      flows: {
        customerRegistrationFlowId: 'reg-flow-123',
        paymentFlowId: 'payment-flow-123',
      },
    } as AppConfig;

    flowService = new FlowService(
      mockFlowSessionRepository as unknown as FlowSessionRepository,
      mockUserService as unknown as UserService,
      mockPaymentService as unknown as PaymentService,
      mockLogger as unknown as Logger,
      mockConfig
    );
  });

  describe('startCustomerRegistrationFlow', () => {
    it('should start new registration flow successfully', async () => {
      const userId = 'user-123';
      const expectedSession = {
        id: 'session-123',
        userId,
        flowType: 'customer_registration',
        flowId: 'reg-flow-123',
        currentStep: 'start',
        status: 'active',
        sessionData: {},
        expiresAt: new Date(Date.now() + 30 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(null);
      mockFlowSessionRepository.create.mockResolvedValue(expectedSession);

      const result = await flowService.startCustomerRegistrationFlow(userId);

      expect(result).toEqual(expectedSession);
      expect(mockFlowSessionRepository.findActiveByUserIdAndType).toHaveBeenCalledWith(
        userId,
        'customer_registration'
      );
      expect(mockFlowSessionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId,
          flowType: 'customer_registration',
          flowId: 'reg-flow-123',
          currentStep: 'start',
          sessionData: {},
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Customer registration flow started',
        expect.objectContaining({
          userId,
          sessionId: expectedSession.id,
        })
      );
    });

    it('should return existing active session', async () => {
      const userId = 'user-123';
      const existingSession = {
        id: 'existing-session-123',
        userId,
        flowType: 'customer_registration',
        status: 'active',
      };

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(existingSession);

      const result = await flowService.startCustomerRegistrationFlow(userId);

      expect(result).toEqual(existingSession);
      expect(mockFlowSessionRepository.create).not.toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Existing registration session found',
        expect.objectContaining({
          userId,
          sessionId: existingSession.id,
        })
      );
    });
  });

  describe('startPaymentFlow', () => {
    it('should start new payment flow successfully', async () => {
      const userId = 'user-123';
      const expectedSession = {
        id: 'session-123',
        userId,
        flowType: 'payment',
        flowId: 'payment-flow-123',
        currentStep: 'product_selection',
        status: 'active',
        sessionData: {
          products: expect.any(Array),
        },
        expiresAt: new Date(Date.now() + 30 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(null);
      mockFlowSessionRepository.create.mockResolvedValue(expectedSession);

      const result = await flowService.startPaymentFlow(userId);

      expect(result).toEqual(expectedSession);
      expect(mockFlowSessionRepository.findActiveByUserIdAndType).toHaveBeenCalledWith(
        userId,
        'payment'
      );
      expect(mockFlowSessionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId,
          flowType: 'payment',
          flowId: 'payment-flow-123',
          currentStep: 'product_selection',
        })
      );
    });

    it('should return existing active payment session', async () => {
      const userId = 'user-123';
      const existingSession = {
        id: 'existing-session-123',
        userId,
        flowType: 'payment',
        status: 'active',
      };

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(existingSession);

      const result = await flowService.startPaymentFlow(userId);

      expect(result).toEqual(existingSession);
      expect(mockFlowSessionRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('processFlowResponse', () => {
    it('should process registration flow response successfully', async () => {
      const userId = 'user-123';
      const flowResponse = {
        screen: 'registration_form',
        data: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
        },
        version: '1.0',
      };

      const activeSession = {
        id: 'session-123',
        userId,
        flowType: 'customer_registration',
        status: 'active',
        sessionData: {},
      };

      mockFlowSessionRepository.findActiveByUserId.mockResolvedValue(activeSession);
      mockUserService.completeUserRegistration.mockResolvedValue({});
      mockFlowSessionRepository.update.mockResolvedValue({});

      await flowService.processFlowResponse(userId, flowResponse);

      expect(mockUserService.completeUserRegistration).toHaveBeenCalledWith(
        userId,
        flowResponse.data.name,
        flowResponse.data.email
      );
      expect(mockFlowSessionRepository.update).toHaveBeenCalledWith(
        activeSession.id,
        expect.objectContaining({
          currentStep: 'completed',
          status: 'completed',
        })
      );
    });

    it('should process payment flow response successfully', async () => {
      const userId = 'user-123';
      const flowResponse = {
        screen: 'product_selection',
        data: {
          selectedProduct: 'premium',
        },
        version: '1.0',
      };

      const activeSession = {
        id: 'session-123',
        userId,
        flowType: 'payment',
        status: 'active',
        sessionData: {},
      };

      mockFlowSessionRepository.findActiveByUserId.mockResolvedValue(activeSession);
      mockFlowSessionRepository.update.mockResolvedValue({});

      await flowService.processFlowResponse(userId, flowResponse);

      expect(mockFlowSessionRepository.update).toHaveBeenCalledWith(
        activeSession.id,
        expect.objectContaining({
          currentStep: 'customer_details',
          sessionData: expect.objectContaining({
            selectedProduct: expect.objectContaining({
              id: 'premium',
            }),
          }),
        })
      );
    });

    it('should handle no active session gracefully', async () => {
      const userId = 'user-123';
      const flowResponse = {
        screen: 'registration_form',
        data: {},
        version: '1.0',
      };

      mockFlowSessionRepository.findActiveByUserId.mockResolvedValue(null);

      await flowService.processFlowResponse(userId, flowResponse);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'No active session found for flow response',
        expect.objectContaining({
          userId,
        })
      );
    });

    it('should handle processing errors', async () => {
      const userId = 'user-123';
      const flowResponse = {
        screen: 'registration_form',
        data: {
          name: 'John Doe',
          email: 'invalid-email', // This should cause validation error
        },
        version: '1.0',
      };

      const activeSession = {
        id: 'session-123',
        userId,
        flowType: 'customer_registration',
        status: 'active',
        sessionData: {},
      };

      mockFlowSessionRepository.findActiveByUserId.mockResolvedValue(activeSession);
      mockUserService.completeUserRegistration.mockRejectedValue(new Error('Invalid email format'));
      mockFlowSessionRepository.update.mockResolvedValue({});

      await flowService.processFlowResponse(userId, flowResponse);

      expect(mockFlowSessionRepository.update).toHaveBeenCalledWith(
        activeSession.id,
        expect.objectContaining({
          status: 'failed',
        })
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error processing flow response',
        expect.objectContaining({
          userId,
          sessionId: activeSession.id,
        })
      );
    });
  });

  describe('selectProduct', () => {
    it('should select product successfully', async () => {
      const userId = 'user-123';
      const productId = 'premium';
      const activeSession = {
        id: 'session-123',
        userId,
        flowType: 'payment',
        status: 'active',
        sessionData: {},
      };

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(activeSession);
      mockFlowSessionRepository.update.mockResolvedValue({});

      await flowService.selectProduct(userId, productId);

      expect(mockFlowSessionRepository.update).toHaveBeenCalledWith(
        activeSession.id,
        expect.objectContaining({
          currentStep: 'customer_details',
          sessionData: expect.objectContaining({
            selectedProduct: expect.objectContaining({
              id: productId,
            }),
          }),
        })
      );
    });

    it('should throw error for no active payment session', async () => {
      const userId = 'user-123';
      const productId = 'premium';

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(null);

      await expect(flowService.selectProduct(userId, productId)).rejects.toThrow('No active payment session');
    });

    it('should throw error for invalid product', async () => {
      const userId = 'user-123';
      const productId = 'invalid-product';
      const activeSession = {
        id: 'session-123',
        userId,
        flowType: 'payment',
        status: 'active',
        sessionData: {},
      };

      mockFlowSessionRepository.findActiveByUserIdAndType.mockResolvedValue(activeSession);

      await expect(flowService.selectProduct(userId, productId)).rejects.toThrow('Invalid product');
    });
  });

  describe('getActiveSession', () => {
    it('should return active session when found', async () => {
      const userId = 'user-123';
      const activeSession = {
        id: 'session-123',
        userId,
        status: 'active',
      };

      mockFlowSessionRepository.findActiveByUserId.mockResolvedValue(activeSession);

      const result = await flowService.getActiveSession(userId);

      expect(result).toEqual(activeSession);
      expect(mockFlowSessionRepository.findActiveByUserId).toHaveBeenCalledWith(userId);
    });

    it('should return null when no active session found', async () => {
      const userId = 'user-123';

      mockFlowSessionRepository.findActiveByUserId.mockResolvedValue(null);

      const result = await flowService.getActiveSession(userId);

      expect(result).toBeNull();
    });
  });

  describe('expireOldSessions', () => {
    it('should expire old sessions successfully', async () => {
      const expiredCount = 5;

      mockFlowSessionRepository.cleanupExpiredSessions.mockResolvedValue(expiredCount);

      const result = await flowService.expireOldSessions();

      expect(result).toBe(expiredCount);
      expect(mockFlowSessionRepository.cleanupExpiredSessions).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Expiring old sessions',
        expect.objectContaining({
          action: 'expire_old_sessions',
        })
      );
    });
  });
});
