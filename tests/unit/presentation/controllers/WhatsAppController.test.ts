import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WhatsAppController } from '@/presentation/controllers/WhatsAppController';
import { 
  createMockWhatsAppService,
  createMockLogger 
} from '../../../mocks/external-apis';
import type { WhatsAppService } from '@/application/services/WhatsAppService';
import type { Logger } from '@/shared/utils/logger';
import type { AppConfig } from '@/shared/types/environment';

describe('WhatsAppController', () => {
  let whatsAppController: WhatsAppController;
  let mockWhatsAppService: ReturnType<typeof createMockWhatsAppService>;
  let mockLogger: ReturnType<typeof createMockLogger>;
  let mockConfig: AppConfig;

  beforeEach(() => {
    mockWhatsAppService = createMockWhatsAppService();
    mockLogger = createMockLogger();
    
    mockConfig = {
      whatsapp: {
        verifyToken: 'test_verify_token',
        webhookSecret: 'test_webhook_secret',
      },
    } as AppConfig;

    whatsAppController = new WhatsAppController(
      mockWhatsAppService as unknown as WhatsAppService,
      mockLogger as unknown as Logger,
      mockConfig
    );
  });

  describe('verifyWebhook', () => {
    it('should verify webhook with correct parameters', async () => {
      const mockRequest = {
        query: {
          'hub.mode': 'subscribe',
          'hub.verify_token': 'test_verify_token',
          'hub.challenge': 'test_challenge_123',
        },
      };

      const result = await whatsAppController.verifyWebhook(mockRequest as any);

      expect(result.status).toBe(200);
      expect(result.body).toBe('test_challenge_123');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Webhook verification successful',
        expect.objectContaining({
          action: 'webhook_verification',
          challenge: 'test_challenge_123',
        })
      );
    });

    it('should reject webhook with wrong verify token', async () => {
      const mockRequest = {
        query: {
          'hub.mode': 'subscribe',
          'hub.verify_token': 'wrong_token',
          'hub.challenge': 'test_challenge_123',
        },
      };

      const result = await whatsAppController.verifyWebhook(mockRequest as any);

      expect(result.status).toBe(403);
      expect(result.body).toEqual({ error: 'Invalid verify token' });
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Webhook verification failed: Invalid verify token',
        expect.objectContaining({
          action: 'webhook_verification_failed',
          providedToken: 'wrong_token',
        })
      );
    });

    it('should reject webhook with missing parameters', async () => {
      const mockRequest = {
        query: {
          'hub.mode': 'subscribe',
        },
      };

      const result = await whatsAppController.verifyWebhook(mockRequest as any);

      expect(result.status).toBe(400);
      expect(result.body).toEqual({ error: 'Missing required parameters' });
    });

    it('should reject webhook with wrong mode', async () => {
      const mockRequest = {
        query: {
          'hub.mode': 'unsubscribe',
          'hub.verify_token': 'test_verify_token',
          'hub.challenge': 'test_challenge_123',
        },
      };

      const result = await whatsAppController.verifyWebhook(mockRequest as any);

      expect(result.status).toBe(400);
      expect(result.body).toEqual({ error: 'Invalid mode' });
    });
  });

  describe('handleWebhook', () => {
    it('should handle text message webhook', async () => {
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: 'test_phone_id',
                  },
                  messages: [
                    {
                      from: '**********',
                      id: 'message_id_123',
                      timestamp: '**********',
                      type: 'text',
                      text: {
                        body: 'hello',
                      },
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const mockRequest = {
        body: webhookPayload,
        headers: {
          'x-hub-signature-256': 'sha256=valid_signature',
        },
      };

      mockWhatsAppService.handleIncomingMessage.mockResolvedValue(undefined);

      const result = await whatsAppController.handleWebhook(mockRequest as any);

      expect(result.status).toBe(200);
      expect(result.body).toEqual({ status: 'success' });
      expect(mockWhatsAppService.handleIncomingMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          from: '**********',
          id: 'message_id_123',
          type: 'text',
          text: { body: 'hello' },
        }),
        expect.objectContaining({
          display_phone_number: '**********',
          phone_number_id: 'test_phone_id',
        })
      );
    });

    it('should handle message status webhook', async () => {
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: 'test_phone_id',
                  },
                  statuses: [
                    {
                      id: 'message_id_123',
                      status: 'delivered',
                      timestamp: '**********',
                      recipient_id: '**********',
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const mockRequest = {
        body: webhookPayload,
        headers: {
          'x-hub-signature-256': 'sha256=valid_signature',
        },
      };

      mockWhatsAppService.handleMessageStatus.mockResolvedValue(undefined);

      const result = await whatsAppController.handleWebhook(mockRequest as any);

      expect(result.status).toBe(200);
      expect(mockWhatsAppService.handleMessageStatus).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'message_id_123',
          status: 'delivered',
          timestamp: '**********',
          recipient_id: '**********',
        })
      );
    });

    it('should handle invalid webhook payload', async () => {
      const mockRequest = {
        body: { invalid: 'payload' },
        headers: {
          'x-hub-signature-256': 'sha256=valid_signature',
        },
      };

      const result = await whatsAppController.handleWebhook(mockRequest as any);

      expect(result.status).toBe(400);
      expect(result.body).toEqual({ error: 'Invalid webhook payload' });
    });

    it('should handle missing signature', async () => {
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [],
      };

      const mockRequest = {
        body: webhookPayload,
        headers: {},
      };

      const result = await whatsAppController.handleWebhook(mockRequest as any);

      expect(result.status).toBe(401);
      expect(result.body).toEqual({ error: 'Missing signature' });
    });

    it('should handle service errors gracefully', async () => {
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: 'test_phone_id',
                  },
                  messages: [
                    {
                      from: '**********',
                      id: 'message_id_123',
                      timestamp: '**********',
                      type: 'text',
                      text: { body: 'hello' },
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const mockRequest = {
        body: webhookPayload,
        headers: {
          'x-hub-signature-256': 'sha256=valid_signature',
        },
      };

      mockWhatsAppService.handleIncomingMessage.mockRejectedValue(new Error('Service error'));

      const result = await whatsAppController.handleWebhook(mockRequest as any);

      expect(result.status).toBe(200); // Still return 200 to WhatsApp
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error processing webhook',
        expect.objectContaining({
          error: 'Service error',
        })
      );
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const result = await whatsAppController.healthCheck();

      expect(result.status).toBe(200);
      expect(result.body).toEqual({
        status: 'healthy',
        service: 'whatsapp-webhook',
        timestamp: expect.any(String),
      });
    });
  });
});
