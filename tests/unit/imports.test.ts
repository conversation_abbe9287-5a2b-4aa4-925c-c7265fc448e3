import { describe, it, expect } from 'vitest';

// Test that all our main service imports work correctly
describe('Import Tests', () => {
  it('should import WhatsAppService without errors', async () => {
    const { WhatsAppService } = await import('@/application/services/WhatsAppService');
    expect(WhatsAppService).toBeDefined();
    expect(typeof WhatsAppService).toBe('function');
  });

  it('should import PaymentService without errors', async () => {
    const { PaymentService } = await import('@/application/services/PaymentService');
    expect(PaymentService).toBeDefined();
    expect(typeof PaymentService).toBe('function');
  });

  it('should import FlowService without errors', async () => {
    const { FlowService } = await import('@/application/services/FlowService');
    expect(FlowService).toBeDefined();
    expect(typeof FlowService).toBe('function');
  });

  it('should import UserService without errors', async () => {
    const { UserService } = await import('@/application/services/UserService');
    expect(UserService).toBeDefined();
    expect(typeof UserService).toBe('function');
  });

  it('should import WhatsAppAdapter without errors', async () => {
    const { WhatsAppAdapter } = await import('@/infrastructure/adapters/WhatsAppAdapter');
    expect(WhatsAppAdapter).toBeDefined();
    expect(typeof WhatsAppAdapter).toBe('function');
  });

  it('should import DpoAdapter without errors', async () => {
    const { DpoAdapter } = await import('@/infrastructure/adapters/DpoAdapter');
    expect(DpoAdapter).toBeDefined();
    expect(typeof DpoAdapter).toBe('function');
  });

  it('should import repositories without errors', async () => {
    const { UserRepository } = await import('@/infrastructure/repositories/UserRepository');
    const { PaymentRepository } = await import('@/infrastructure/repositories/PaymentRepository');
    const { FlowSessionRepository } = await import('@/infrastructure/repositories/FlowSessionRepository');
    
    expect(UserRepository).toBeDefined();
    expect(PaymentRepository).toBeDefined();
    expect(FlowSessionRepository).toBeDefined();
  });

  it('should import utilities without errors', async () => {
    const { CryptoUtils } = await import('@/shared/utils/crypto');
    const { Logger } = await import('@/shared/utils/logger');
    const validation = await import('@/shared/utils/validation');
    
    expect(CryptoUtils).toBeDefined();
    expect(Logger).toBeDefined();
    expect(validation).toBeDefined();
  });

  it('should import domain entities without errors', async () => {
    const { User } = await import('@/domain/entities/User');
    const { Payment } = await import('@/domain/entities/Payment');
    const { FlowSession } = await import('@/domain/entities/FlowSession');
    
    expect(User).toBeDefined();
    expect(Payment).toBeDefined();
    expect(FlowSession).toBeDefined();
  });

  it('should import mock factories without errors', async () => {
    const mocks = await import('../mocks/external-apis');
    
    expect(mocks.createMockWhatsAppAdapter).toBeDefined();
    expect(mocks.createMockDpoAdapter).toBeDefined();
    expect(mocks.createMockUserRepository).toBeDefined();
    expect(mocks.createMockPaymentRepository).toBeDefined();
    expect(mocks.createMockFlowSessionRepository).toBeDefined();
    expect(mocks.createMockLogger).toBeDefined();
    expect(mocks.createMockUserService).toBeDefined();
    expect(mocks.createMockPaymentService).toBeDefined();
    expect(mocks.createMockFlowService).toBeDefined();
    expect(mocks.TestDataGenerator).toBeDefined();
  });

  it('should create mock instances without errors', async () => {
    const {
      createMockWhatsAppAdapter,
      createMockDpoAdapter,
      createMockUserRepository,
      createMockPaymentRepository,
      createMockFlowSessionRepository,
      createMockLogger,
      createMockUserService,
      createMockPaymentService,
      createMockFlowService,
      TestDataGenerator,
    } = await import('../mocks/external-apis');

    // Test that all mock factories work
    expect(() => createMockWhatsAppAdapter()).not.toThrow();
    expect(() => createMockDpoAdapter()).not.toThrow();
    expect(() => createMockUserRepository()).not.toThrow();
    expect(() => createMockPaymentRepository()).not.toThrow();
    expect(() => createMockFlowSessionRepository()).not.toThrow();
    expect(() => createMockLogger()).not.toThrow();
    expect(() => createMockUserService()).not.toThrow();
    expect(() => createMockPaymentService()).not.toThrow();
    expect(() => createMockFlowService()).not.toThrow();

    // Test TestDataGenerator methods
    expect(() => TestDataGenerator.generateId()).not.toThrow();
    expect(() => TestDataGenerator.generatePhoneNumber()).not.toThrow();
    expect(() => TestDataGenerator.generateEmail()).not.toThrow();
    expect(() => TestDataGenerator.generateName()).not.toThrow();
    expect(() => TestDataGenerator.generateAmount()).not.toThrow();
    expect(() => TestDataGenerator.generateProductName()).not.toThrow();

    // Verify generated data types
    expect(typeof TestDataGenerator.generateId()).toBe('string');
    expect(typeof TestDataGenerator.generatePhoneNumber()).toBe('string');
    expect(typeof TestDataGenerator.generateEmail()).toBe('string');
    expect(typeof TestDataGenerator.generateName()).toBe('string');
    expect(typeof TestDataGenerator.generateAmount()).toBe('number');
    expect(typeof TestDataGenerator.generateProductName()).toBe('string');
  });
});
