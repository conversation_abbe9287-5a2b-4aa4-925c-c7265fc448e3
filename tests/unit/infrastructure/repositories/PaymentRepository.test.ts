import { describe, it, expect, beforeEach } from 'vitest';
import { PaymentRepository } from '@/infrastructure/repositories/PaymentRepository';
import { CryptoUtils } from '@/shared/utils/crypto';
import { env } from 'cloudflare:test';
import { payments, users } from '@/infrastructure/database/schema';
import { TestDataGenerator } from '../../../mocks/external-apis';

describe('PaymentRepository Integration', () => {
  let paymentRepository: PaymentRepository;
  let cryptoUtils: CryptoUtils;

  beforeEach(async () => {
    cryptoUtils = new CryptoUtils('test_encryption_key_32_chars_long');
    // Note: PaymentRepository would need to be adapted to work with D1 directly
    // For now, we'll skip the actual database operations in this test
    // paymentRepository = new PaymentRepository(env.TEST_DB, cryptoUtils);

    // Clean database using D1
    try {
      await env.TEST_DB.exec(`
        DELETE FROM payments;
        DELETE FROM users;
      `);

      // Create a test user
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, name, email, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'test-user-1',
        '+1234567890',
        'Test User',
        '<EMAIL>',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();
    } catch (error) {
      console.warn('Database setup failed in test:', error);
    }
  });

  describe('create', () => {
    it('should create a new payment successfully', async () => {
      const paymentData = {
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: TestDataGenerator.generateAmount(),
        currency: 'USD',
        productName: TestDataGenerator.generateProductName(),
        customerEmail: TestDataGenerator.generateEmail(),
        customerPhone: TestDataGenerator.generatePhoneNumber(),
      };

      const result = await paymentRepository.create(paymentData);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.userId).toBe(paymentData.userId);
      expect(result.amount).toBe(paymentData.amount);
      expect(result.currency).toBe(paymentData.currency);
      expect(result.status).toBe('pending');
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('should create payment with optional fields', async () => {
      const paymentData = {
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        productDescription: 'Test Description',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      const result = await paymentRepository.create(paymentData);

      expect(result.productDescription).toBe(paymentData.productDescription);
    });

    it('should throw error for invalid user ID', async () => {
      const paymentData = {
        userId: 'non-existent-user',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      await expect(paymentRepository.create(paymentData)).rejects.toThrow();
    });
  });

  describe('findById', () => {
    it('should find payment by ID', async () => {
      const paymentData = {
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      const createdPayment = await paymentRepository.create(paymentData);
      const foundPayment = await paymentRepository.findById(createdPayment.id);

      expect(foundPayment).toBeDefined();
      expect(foundPayment?.id).toBe(createdPayment.id);
      expect(foundPayment?.amount).toBe(paymentData.amount);
    });

    it('should return null for non-existent ID', async () => {
      const result = await paymentRepository.findById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('findByDpoToken', () => {
    it('should find payment by DPO token', async () => {
      const dpoToken = TestDataGenerator.generateId();
      const paymentData = {
        userId: 'test-user-1',
        dpoTransactionToken: dpoToken,
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      await paymentRepository.create(paymentData);
      const foundPayment = await paymentRepository.findByDpoToken(dpoToken);

      expect(foundPayment).toBeDefined();
      expect(foundPayment?.dpoTransactionToken).toBe(dpoToken);
    });

    it('should return null for non-existent token', async () => {
      const result = await paymentRepository.findByDpoToken('non-existent-token');
      expect(result).toBeNull();
    });
  });

  describe('findByUserId', () => {
    it('should find payments by user ID', async () => {
      const userId = 'test-user-1';
      
      // Create multiple payments for the user
      await paymentRepository.create({
        userId,
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Product 1',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      await paymentRepository.create({
        userId,
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 199.99,
        currency: 'USD',
        productName: 'Product 2',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      const userPayments = await paymentRepository.findByUserId(userId);

      expect(userPayments).toHaveLength(2);
      userPayments.forEach(payment => {
        expect(payment.userId).toBe(userId);
      });
    });

    it('should return empty array for user with no payments', async () => {
      const result = await paymentRepository.findByUserId('test-user-1');
      expect(result).toEqual([]);
    });
  });

  describe('findByStatus', () => {
    it('should find payments by status', async () => {
      // Create payments with different statuses
      const payment1 = await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Product 1',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      const payment2 = await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 199.99,
        currency: 'USD',
        productName: 'Product 2',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      // Update one payment to completed
      await paymentRepository.update(payment2.id, { status: 'completed' });

      const pendingPayments = await paymentRepository.findByStatus('pending');
      const completedPayments = await paymentRepository.findByStatus('completed');

      expect(pendingPayments).toHaveLength(1);
      expect(pendingPayments[0].id).toBe(payment1.id);
      expect(completedPayments).toHaveLength(1);
      expect(completedPayments[0].id).toBe(payment2.id);
    });
  });

  describe('update', () => {
    it('should update payment successfully', async () => {
      const paymentData = {
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      const createdPayment = await paymentRepository.create(paymentData);
      
      const updateData = {
        status: 'completed' as const,
        dpoPaymentReference: 'DPO_REF_123',
        completedAt: new Date(),
      };

      const updatedPayment = await paymentRepository.update(createdPayment.id, updateData);

      expect(updatedPayment).toBeDefined();
      expect(updatedPayment?.status).toBe(updateData.status);
      expect(updatedPayment?.dpoPaymentReference).toBe(updateData.dpoPaymentReference);
      expect(updatedPayment?.completedAt).toBeDefined();
      expect(new Date(updatedPayment!.updatedAt).getTime()).toBeGreaterThan(
        new Date(createdPayment.updatedAt).getTime()
      );
    });

    it('should return null for non-existent payment', async () => {
      const result = await paymentRepository.update('non-existent-id', {
        status: 'completed',
      });

      expect(result).toBeNull();
    });

    it('should update partial data', async () => {
      const paymentData = {
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      };

      const createdPayment = await paymentRepository.create(paymentData);
      
      const updatedPayment = await paymentRepository.update(createdPayment.id, {
        dpoPaymentReference: 'DPO_REF_123',
      });

      expect(updatedPayment).toBeDefined();
      expect(updatedPayment?.dpoPaymentReference).toBe('DPO_REF_123');
      expect(updatedPayment?.status).toBe('pending'); // Should remain unchanged
    });
  });

  describe('getPaymentStats', () => {
    it('should return correct payment statistics', async () => {
      // Create payments with different statuses
      await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 100,
        currency: 'USD',
        productName: 'Product 1',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      const payment2 = await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 200,
        currency: 'USD',
        productName: 'Product 2',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      const payment3 = await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 300,
        currency: 'USD',
        productName: 'Product 3',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      // Update statuses
      await paymentRepository.update(payment2.id, { status: 'completed' });
      await paymentRepository.update(payment3.id, { status: 'failed' });

      const stats = await paymentRepository.getPaymentStats();

      expect(stats.total).toBe(3);
      expect(stats.pending).toBe(1);
      expect(stats.completed).toBe(1);
      expect(stats.failed).toBe(1);
      expect(stats.totalAmount).toBe(600);
      expect(stats.completedAmount).toBe(200);
    });

    it('should return zero stats for empty database', async () => {
      const stats = await paymentRepository.getPaymentStats();

      expect(stats.total).toBe(0);
      expect(stats.pending).toBe(0);
      expect(stats.completed).toBe(0);
      expect(stats.failed).toBe(0);
      expect(stats.totalAmount).toBe(0);
      expect(stats.completedAmount).toBe(0);
    });
  });

  describe('findExpiredPayments', () => {
    it('should find expired payments', async () => {
      // Create a payment
      const payment = await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      // Manually update the created date to make it expired
      const expiredTime = new Date(Date.now() - 20 * 60 * 1000); // 20 minutes ago
      await testDb.update(payments)
        .set({ createdAt: expiredTime.toISOString() })
        .where({ id: payment.id });

      const expiredPayments = await paymentRepository.findExpiredPayments(15); // 15 minute timeout

      expect(expiredPayments).toHaveLength(1);
      expect(expiredPayments[0].id).toBe(payment.id);
    });

    it('should not return non-expired payments', async () => {
      await paymentRepository.create({
        userId: 'test-user-1',
        dpoTransactionToken: TestDataGenerator.generateId(),
        amount: 99.99,
        currency: 'USD',
        productName: 'Test Product',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
      });

      const expiredPayments = await paymentRepository.findExpiredPayments(15);

      expect(expiredPayments).toHaveLength(0);
    });
  });
});
