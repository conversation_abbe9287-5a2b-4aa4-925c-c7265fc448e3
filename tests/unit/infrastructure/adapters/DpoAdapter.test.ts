import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DpoAdapter } from '@/infrastructure/adapters/DpoAdapter';
import { createMockLogger } from '../../../mocks/external-apis';
import { server } from '../../../setup';
import { http, HttpResponse } from 'msw';

describe('DpoAdapter', () => {
  let dpoAdapter: DpoAdapter;
  let mockLogger: ReturnType<typeof createMockLogger>;

  const mockConfig = {
    companyToken: 'test_company_token',
    serviceType: 'test_service',
    paymentUrl: 'https://secure.3gdirectpay.com',
    paymentApi: 'https://secure.3gdirectpay.com/payv2.php',
  };

  beforeEach(() => {
    mockLogger = createMockLogger();
    dpoAdapter = new DpoAdapter(mockConfig, mockLogger as any);
  });

  describe('createPaymentToken', () => {
    it('should create payment token successfully', async () => {
      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        productDescription: 'Test Description',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      const result = await dpoAdapter.createPaymentToken(request);

      expect(result.success).toBe(true);
      expect(result.reference).toBeDefined();
      expect(result.paymentUrl).toContain('secure.3gdirectpay.com');
      expect(result.transactionToken).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'DPO payment token created successfully',
        expect.objectContaining({
          transactionToken: request.transactionToken,
        })
      );
    });

    it('should handle DPO API errors', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.json({
            Result: '001',
            ResultExplanation: 'Invalid company token',
          });
        })
      );

      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      await expect(dpoAdapter.createPaymentToken(request)).rejects.toThrow('DPO API error: Invalid company token');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to create DPO payment token',
        expect.objectContaining({
          transactionToken: request.transactionToken,
        })
      );
    });

    it('should handle network errors', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.error();
        })
      );

      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      await expect(dpoAdapter.createPaymentToken(request)).rejects.toThrow('Failed to create payment token');
    });

    it('should send correct payload structure', async () => {
      let capturedPayload: any = {};
      
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
          capturedPayload = await request.json();
          return HttpResponse.json({
            Result: '000',
            ResultExplanation: 'Transaction created successfully',
            TransToken: 'TEST_TOKEN_123',
            TransRef: 'TEST_REF_123',
          });
        })
      );

      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        productDescription: 'Test Description',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      await dpoAdapter.createPaymentToken(request);

      expect(capturedPayload).toEqual({
        API: '1.0',
        Request: 'createToken',
        Transaction: {
          PaymentAmount: '99.99',
          PaymentCurrency: 'USD',
          CompanyRef: 'test_transaction_token',
          RedirectURL: 'https://example.com/success',
          BackURL: 'https://example.com/cancel',
          CompanyRefUnique: '1',
          PTL: '5',
          PTLtype: 'minutes',
        },
        Services: {
          Service: {
            ServiceType: 'test_service',
            ServiceDescription: 'Test Product',
            ServiceDate: expect.any(String),
          },
        },
        Customer: {
          CustomerEmail: '<EMAIL>',
          CustomerPhone: '+1234567890',
        },
        CompanyToken: 'test_company_token',
      });
    });
  });

  describe('verifyPayment', () => {
    it('should verify completed payment successfully', async () => {
      const request = {
        transactionToken: 'test_token',
        companyRef: 'test_ref',
      };

      const result = await dpoAdapter.verifyPayment(request);

      expect(result.success).toBe(true);
      expect(result.status).toBe('completed');
      expect(result.amount).toBe(99.99);
      expect(result.currency).toBe('USD');
      expect(result.customerEmail).toBe('<EMAIL>');
      expect(result.customerPhone).toBe('+1234567890');
      expect(result.transactionToken).toBe(request.transactionToken);
      expect(result.reference).toBe('REF_SUCCESS');
    });

    it('should verify failed payment', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
          const body = await request.json() as any;
          if (body.Request === 'verifyToken') {
            return HttpResponse.json({
              Result: '000',
              ResultExplanation: 'Transaction verified',
              TransactionStatus: '2', // Failed
              TransactionAmount: '99.99',
              TransactionCurrency: 'USD',
              CustomerEmail: '<EMAIL>',
              CustomerPhone: '+1234567890',
              TransactionRef: 'REF_FAILED',
            });
          }
          return HttpResponse.json({ Result: '001' });
        })
      );

      const request = {
        transactionToken: 'failed_token',
      };

      const result = await dpoAdapter.verifyPayment(request);

      expect(result.success).toBe(true);
      expect(result.status).toBe('failed');
    });

    it('should verify cancelled payment', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
          const body = await request.json() as any;
          if (body.Request === 'verifyToken') {
            return HttpResponse.json({
              Result: '000',
              ResultExplanation: 'Transaction verified',
              TransactionStatus: '4', // Cancelled
              TransactionAmount: '99.99',
              TransactionCurrency: 'USD',
              CustomerEmail: '<EMAIL>',
              CustomerPhone: '+1234567890',
              TransactionRef: 'REF_CANCELLED',
            });
          }
          return HttpResponse.json({ Result: '001' });
        })
      );

      const request = {
        transactionToken: 'cancelled_token',
      };

      const result = await dpoAdapter.verifyPayment(request);

      expect(result.success).toBe(true);
      expect(result.status).toBe('cancelled');
    });

    it('should handle verification API errors', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.error();
        })
      );

      const request = {
        transactionToken: 'test_token',
      };

      await expect(dpoAdapter.verifyPayment(request)).rejects.toThrow('Failed to verify payment');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to verify DPO payment',
        expect.objectContaining({
          transactionToken: request.transactionToken,
        })
      );
    });

    it('should send correct verification payload', async () => {
      let capturedPayload: any = {};
      
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
          capturedPayload = await request.json();
          return HttpResponse.json({
            Result: '000',
            ResultExplanation: 'Transaction verified successfully',
            TransactionStatus: '1',
            TransactionAmount: '99.99',
            TransactionCurrency: 'USD',
            CustomerEmail: '<EMAIL>',
            CustomerPhone: '+1234567890',
            TransactionRef: 'REF_SUCCESS',
          });
        })
      );

      const request = {
        transactionToken: 'test_token',
        companyRef: 'test_ref',
      };

      await dpoAdapter.verifyPayment(request);

      expect(capturedPayload).toEqual({
        API: '1.0',
        Request: 'verifyToken',
        TransactionToken: 'test_token',
        CompanyToken: 'test_company_token',
      });
    });
  });

  describe('cancelPayment', () => {
    it('should cancel payment successfully', async () => {
      const transactionToken = 'test_token';

      const result = await dpoAdapter.cancelPayment(transactionToken);

      expect(result).toBe(true);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'DPO payment cancellation completed',
        expect.objectContaining({
          transactionToken,
          success: true,
        })
      );
    });

    it('should handle cancellation errors', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.json({
            Result: '001',
            ResultExplanation: 'Cancellation failed',
          });
        })
      );

      const transactionToken = 'test_token';

      const result = await dpoAdapter.cancelPayment(transactionToken);

      expect(result).toBe(false);
    });

    it('should handle network errors gracefully', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.error();
        })
      );

      const transactionToken = 'test_token';

      const result = await dpoAdapter.cancelPayment(transactionToken);

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to cancel DPO payment',
        expect.objectContaining({
          transactionToken,
        })
      );
    });
  });

  describe('getPaymentMethods', () => {
    it('should retrieve payment methods successfully', async () => {
      const result = await dpoAdapter.getPaymentMethods();

      expect(result).toEqual([
        {
          id: 'card',
          name: 'Credit/Debit Card',
          description: 'Visa, Mastercard, American Express',
        },
        {
          id: 'mobile',
          name: 'Mobile Money',
          description: 'M-Pesa, Airtel Money, etc.',
        },
      ]);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'DPO payment methods retrieved successfully',
        expect.objectContaining({
          methodCount: 2,
        })
      );
    });

    it('should handle API errors gracefully', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.json({
            Result: '001',
            ResultExplanation: 'Invalid request',
          });
        })
      );

      const result = await dpoAdapter.getPaymentMethods();

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get DPO payment methods',
        expect.any(Object)
      );
    });

    it('should handle network errors gracefully', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.error();
        })
      );

      const result = await dpoAdapter.getPaymentMethods();

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON response', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return new Response('invalid json', {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        })
      );

      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      await expect(dpoAdapter.createPaymentToken(request)).rejects.toThrow();
    });

    it('should handle HTTP errors', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return new Response('Server Error', { status: 500 });
        })
      );

      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      await expect(dpoAdapter.createPaymentToken(request)).rejects.toThrow('DPO API error: 500');
    });

    it('should handle missing response fields', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', () => {
          return HttpResponse.json({
            Result: '000',
            // Missing required fields
          });
        })
      );

      const request = {
        amount: 99.99,
        currency: 'USD',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        productName: 'Test Product',
        transactionToken: 'test_transaction_token',
        redirectUrl: 'https://example.com/success',
        backUrl: 'https://example.com/cancel',
      };

      const result = await dpoAdapter.createPaymentToken(request);

      // Should handle missing fields gracefully
      expect(result.success).toBe(true);
      expect(result.reference).toBeDefined();
    });
  });

  describe('Request Types', () => {
    it('should handle unknown request types', async () => {
      server.use(
        http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
          const body = await request.json() as any;
          
          // Return unknown request response for any unrecognized request
          if (!['createToken', 'verifyToken', 'cancelToken', 'getPaymentMethods'].includes(body.Request)) {
            return HttpResponse.json({
              Result: '001',
              ResultExplanation: 'Unknown request type',
            });
          }
          
          return HttpResponse.json({
            Result: '000',
            ResultExplanation: 'Success',
          });
        })
      );

      // This should still work as our adapter only sends known request types
      const result = await dpoAdapter.getPaymentMethods();
      expect(result).toEqual([]);
    });
  });
});
