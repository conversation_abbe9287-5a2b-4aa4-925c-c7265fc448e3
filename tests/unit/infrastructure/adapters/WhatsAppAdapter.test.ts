import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WhatsAppAdapter } from '@/infrastructure/adapters/WhatsAppAdapter';
import { createMockLogger } from '../../../mocks/external-apis';

describe('WhatsAppAdapter', () => {
  let whatsAppAdapter: WhatsAppAdapter;
  let mockLogger: ReturnType<typeof createMockLogger>;

  const mockConfig = {
    verifyToken: 'test_verify_token',
    accessToken: 'test_access_token',
    phoneNumberId: 'test_phone_id',
    webhookSecret: 'test_webhook_secret',
  };

  beforeEach(() => {
    mockLogger = createMockLogger();
    whatsAppAdapter = new WhatsAppAdapter(mockConfig, mockLogger as any);
  });

  describe('sendTextMessage', () => {
    it('should send text message successfully', async () => {
      const request = {
        to: '+1234567890',
        text: 'Hello, World!',
      };

      const result = await whatsAppAdapter.sendTextMessage(request);

      expect(result.messageId).toBeDefined();
      expect(result.messageId).toMatch(/^wamid\.test/);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Text message sent successfully',
        expect.objectContaining({
          to: request.to,
          messageId: result.messageId,
        })
      );
    });

    it('should handle API errors', async () => {
      // Mock error response
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return HttpResponse.json({
            error: {
              message: 'Invalid phone number',
              type: 'OAuthException',
              code: 100,
            },
          }, { status: 400 });
        })
      );

      const request = {
        to: 'invalid_phone',
        text: 'Hello, World!',
      };

      await expect(whatsAppAdapter.sendTextMessage(request)).rejects.toThrow('WhatsApp API error: 400');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to send text message',
        expect.objectContaining({
          to: request.to,
        })
      );
    });

    it('should handle network errors', async () => {
      // Mock network error
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return HttpResponse.error();
        })
      );

      const request = {
        to: '+1234567890',
        text: 'Hello, World!',
      };

      await expect(whatsAppAdapter.sendTextMessage(request)).rejects.toThrow();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle invalid API response', async () => {
      // Mock invalid response
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return HttpResponse.json({
            // Missing messages array
            messaging_product: 'whatsapp',
          });
        })
      );

      const request = {
        to: '+1234567890',
        text: 'Hello, World!',
      };

      await expect(whatsAppAdapter.sendTextMessage(request)).rejects.toThrow('Invalid response from WhatsApp API');
    });
  });

  describe('sendInteractiveMessage', () => {
    it('should send button message successfully', async () => {
      const request = {
        to: '+1234567890',
        type: 'button' as const,
        body: {
          text: 'Choose an option:',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'option_1',
                title: 'Option 1',
              },
            },
          ],
        },
      };

      const result = await whatsAppAdapter.sendInteractiveMessage(request);

      expect(result.messageId).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Interactive message sent successfully',
        expect.objectContaining({
          to: request.to,
          type: request.type,
        })
      );
    });

    it('should send list message successfully', async () => {
      const request = {
        to: '+1234567890',
        type: 'list' as const,
        body: {
          text: 'Select from the list:',
        },
        action: {
          button: 'View Options',
          sections: [
            {
              title: 'Options',
              rows: [
                {
                  id: 'option_1',
                  title: 'Option 1',
                  description: 'First option',
                },
              ],
            },
          ],
        },
      };

      const result = await whatsAppAdapter.sendInteractiveMessage(request);

      expect(result.messageId).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Interactive message sent successfully',
        expect.objectContaining({
          to: request.to,
          type: request.type,
        })
      );
    });

    it('should include optional header and footer', async () => {
      const request = {
        to: '+1234567890',
        type: 'button' as const,
        header: {
          type: 'text' as const,
          text: 'Header Text',
        },
        body: {
          text: 'Body text',
        },
        footer: {
          text: 'Footer text',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'option_1',
                title: 'Option 1',
              },
            },
          ],
        },
      };

      const result = await whatsAppAdapter.sendInteractiveMessage(request);

      expect(result.messageId).toBeDefined();
    });
  });

  describe('sendFlowMessage', () => {
    it('should send flow message successfully', async () => {
      const request = {
        to: '+1234567890',
        flowId: 'test_flow_id',
        flowCta: 'Start Flow',
        flowAction: 'navigate',
        flowActionPayload: {
          screen: 'welcome',
        },
        body: {
          text: 'Please complete the flow',
        },
      };

      const result = await whatsAppAdapter.sendFlowMessage(request);

      expect(result.messageId).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Flow message sent successfully',
        expect.objectContaining({
          to: request.to,
          flowId: request.flowId,
        })
      );
    });

    it('should send flow message with optional header and footer', async () => {
      const request = {
        to: '+1234567890',
        flowId: 'test_flow_id',
        flowCta: 'Start Flow',
        flowAction: 'navigate',
        header: {
          type: 'text' as const,
          text: 'Flow Header',
        },
        body: {
          text: 'Please complete the flow',
        },
        footer: {
          text: 'Flow Footer',
        },
      };

      const result = await whatsAppAdapter.sendFlowMessage(request);

      expect(result.messageId).toBeDefined();
    });

    it('should handle flow message without action payload', async () => {
      const request = {
        to: '+1234567890',
        flowId: 'test_flow_id',
        flowCta: 'Start Flow',
        flowAction: 'navigate',
        body: {
          text: 'Please complete the flow',
        },
      };

      const result = await whatsAppAdapter.sendFlowMessage(request);

      expect(result.messageId).toBeDefined();
    });
  });

  describe('markMessageAsRead', () => {
    it('should mark message as read successfully', async () => {
      const messageId = 'test_message_id';

      await whatsAppAdapter.markMessageAsRead(messageId);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Message marked as read successfully',
        expect.objectContaining({
          messageId,
        })
      );
    });

    it('should handle API errors gracefully', async () => {
      // Mock error response for read receipt
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return HttpResponse.json({
            error: {
              message: 'Invalid message ID',
              type: 'OAuthException',
              code: 100,
            },
          }, { status: 400 });
        })
      );

      const messageId = 'invalid_message_id';

      // Should not throw error for read receipts
      await expect(whatsAppAdapter.markMessageAsRead(messageId)).resolves.not.toThrow();
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to mark message as read',
        expect.objectContaining({
          messageId,
        })
      );
    });

    it('should handle network errors gracefully', async () => {
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return HttpResponse.error();
        })
      );

      const messageId = 'test_message_id';

      // Should not throw error for read receipts
      await expect(whatsAppAdapter.markMessageAsRead(messageId)).resolves.not.toThrow();
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON response', async () => {
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return new Response('invalid json', {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        })
      );

      const request = {
        to: '+1234567890',
        text: 'Hello, World!',
      };

      await expect(whatsAppAdapter.sendTextMessage(request)).rejects.toThrow();
    });

    it('should handle empty response', async () => {
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
          return new Response('', { status: 200 });
        })
      );

      const request = {
        to: '+1234567890',
        text: 'Hello, World!',
      };

      await expect(whatsAppAdapter.sendTextMessage(request)).rejects.toThrow();
    });

    it('should handle timeout errors', async () => {
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', async () => {
          // Simulate timeout
          await new Promise(resolve => setTimeout(resolve, 100));
          return HttpResponse.error();
        })
      );

      const request = {
        to: '+1234567890',
        text: 'Hello, World!',
      };

      await expect(whatsAppAdapter.sendTextMessage(request)).rejects.toThrow();
    });
  });

  describe('Request Validation', () => {
    it('should construct correct API URL', async () => {
      let capturedUrl = '';
      
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', ({ request, params }) => {
          capturedUrl = request.url;
          expect(params.phoneNumberId).toBe('test_phone_id');
          return HttpResponse.json({
            messaging_product: 'whatsapp',
            contacts: [{ input: '+1234567890', wa_id: '1234567890' }],
            messages: [{ id: 'wamid.test123' }],
          });
        })
      );

      await whatsAppAdapter.sendTextMessage({
        to: '+1234567890',
        text: 'Hello, World!',
      });

      expect(capturedUrl).toContain('test_phone_id');
    });

    it('should include correct headers', async () => {
      let capturedHeaders: Record<string, string> = {};
      
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', ({ request }) => {
          capturedHeaders = Object.fromEntries(request.headers.entries());
          return HttpResponse.json({
            messaging_product: 'whatsapp',
            contacts: [{ input: '+1234567890', wa_id: '1234567890' }],
            messages: [{ id: 'wamid.test123' }],
          });
        })
      );

      await whatsAppAdapter.sendTextMessage({
        to: '+1234567890',
        text: 'Hello, World!',
      });

      expect(capturedHeaders.authorization).toBe('Bearer test_access_token');
      expect(capturedHeaders['content-type']).toBe('application/json');
    });

    it('should send correct payload structure', async () => {
      let capturedPayload: any = {};
      
      server.use(
        http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', async ({ request }) => {
          capturedPayload = await request.json();
          return HttpResponse.json({
            messaging_product: 'whatsapp',
            contacts: [{ input: '+1234567890', wa_id: '1234567890' }],
            messages: [{ id: 'wamid.test123' }],
          });
        })
      );

      await whatsAppAdapter.sendTextMessage({
        to: '+1234567890',
        text: 'Hello, World!',
      });

      expect(capturedPayload).toEqual({
        messaging_product: 'whatsapp',
        to: '+1234567890',
        type: 'text',
        text: {
          body: 'Hello, World!',
        },
      });
    });
  });
});
