// Type definitions for Cloudflare Workers testing environment

declare module "cloudflare:test" {
  // ProvidedEnv controls the type of `import("cloudflare:test").env`
  interface ProvidedEnv {
    // Database bindings
    DB: D1Database;
    TEST_DB: D1Database;
    
    // KV bindings
    TEST_KV: KVNamespace;
    
    // Environment variables
    WHATSAPP_VERIFY_TOKEN: string;
    WHATSAPP_ACCESS_TOKEN: string;
    WHATSAPP_PHONE_NUMBER_ID: string;
    WHATSAPP_WEBHOOK_SECRET: string;
    
    DPO_COMPANY_TOKEN: string;
    DPO_SERVICE_TYPE: string;
    DPO_PAYMENT_URL: string;
    DPO_PAYMENT_API: string;
    
    APP_URL: string;
    ENVIRONMENT: string;
    JWT_SECRET: string;
    ENCRYPTION_KEY: string;
    LOG_LEVEL: string;
    ENABLE_REQUEST_LOGGING: string;
    
    RATE_LIMIT_REQUESTS_PER_MINUTE: string;
    RATE_LIMIT_BURST_SIZE: string;
    
    CUSTOMER_REGISTRATION_FLOW_ID: string;
    PAYMENT_FLOW_ID: string;
    DEFAULT_CURRENCY: string;
    PAYMENT_TIMEOUT_MINUTES: string;
    SESSION_TIMEOUT_MINUTES: string;
  }
}

// Global types for testing
declare global {
  // Cloudflare Workers runtime types
  interface CloudflareEnv extends ProvidedEnv {}
  
  // Test utilities
  interface TestContext {
    env: ProvidedEnv;
    executionContext: ExecutionContext;
  }
}

export {};
