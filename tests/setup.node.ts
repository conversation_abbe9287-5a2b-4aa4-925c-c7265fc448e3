import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

// Mock server for external API calls in Node.js environment
export const server = setupServer(
  // WhatsApp API mocks
  http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', () => {
    return HttpResponse.json({
      messaging_product: 'whatsapp',
      contacts: [{ input: '+1234567890', wa_id: '1234567890' }],
      messages: [{ id: 'wamid.test123' }],
    });
  }),

  // DPO Pay API mocks
  http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
    const body = await request.text();
    
    if (body.includes('createToken')) {
      return HttpResponse.text(`
        <?xml version="1.0" encoding="utf-8"?>
        <API3G>
          <Result>000</Result>
          <ResultExplanation>Transaction created successfully</ResultExplanation>
          <TransToken>TEST_TOKEN_123</TransToken>
          <TransRef>TEST_REF_123</TransRef>
        </API3G>
      `);
    }
    
    if (body.includes('verifyToken')) {
      return HttpResponse.text(`
        <?xml version="1.0" encoding="utf-8"?>
        <API3G>
          <Result>000</Result>
          <ResultExplanation>Transaction verified successfully</ResultExplanation>
          <TransactionStatus>1</TransactionStatus>
          <TransactionAmount>9999</TransactionAmount>
          <TransactionCurrency>USD</TransactionCurrency>
          <CustomerEmail><EMAIL></CustomerEmail>
          <CustomerPhone>+1234567890</CustomerPhone>
          <TransactionRef>TEST_REF_123</TransactionRef>
        </API3G>
      `);
    }

    return HttpResponse.text(`
      <?xml version="1.0" encoding="utf-8"?>
      <API3G>
        <Result>001</Result>
        <ResultExplanation>Unknown request</ResultExplanation>
      </API3G>
    `);
  }),
);

beforeAll(async () => {
  // Start mock server
  server.listen({ onUnhandledRequest: 'error' });

  // Mock Cloudflare Workers environment for Node.js
  mockCloudflareWorkersEnvironment();
});

beforeEach(async () => {
  // Reset all mocks before each test
  vi.clearAllMocks();
});

afterEach(() => {
  // Reset MSW handlers
  server.resetHandlers();
});

afterAll(() => {
  // Stop mock server
  server.close();
});

// Mock Cloudflare Workers environment for Node.js testing
function mockCloudflareWorkersEnvironment() {
  // Mock D1 Database
  const mockD1 = {
    prepare: vi.fn().mockReturnValue({
      bind: vi.fn().mockReturnValue({
        run: vi.fn().mockResolvedValue({ success: true, changes: 1 }),
        first: vi.fn().mockResolvedValue(null),
        all: vi.fn().mockResolvedValue({ results: [] }),
      }),
    }),
    exec: vi.fn().mockResolvedValue({ success: true }),
    batch: vi.fn().mockResolvedValue([{ success: true }]),
  };

  // Mock KV Namespace
  const mockKV = {
    get: vi.fn().mockResolvedValue(null),
    put: vi.fn().mockResolvedValue(undefined),
    delete: vi.fn().mockResolvedValue(undefined),
    list: vi.fn().mockResolvedValue({ keys: [] }),
  };

  // Mock environment variables
  const mockEnv = {
    DB: mockD1,
    TEST_DB: mockD1,
    KV: mockKV,
    TEST_KV: mockKV,
    WHATSAPP_VERIFY_TOKEN: 'test_verify_token',
    WHATSAPP_ACCESS_TOKEN: 'test_access_token',
    WHATSAPP_PHONE_NUMBER_ID: 'test_phone_id',
    WHATSAPP_WEBHOOK_SECRET: 'test_webhook_secret',
    DPO_COMPANY_TOKEN: 'test_dpo_token',
    DPO_SERVICE_TYPE: 'test_service',
    DPO_PAYMENT_URL: 'https://secure.3gdirectpay.com',
    DPO_PAYMENT_API: 'https://secure.3gdirectpay.com/payv2.php',
    APP_URL: 'https://test.workers.dev',
    ENVIRONMENT: 'test',
    JWT_SECRET: 'test_jwt_secret_32_characters_long',
    ENCRYPTION_KEY: 'test_encryption_key_32_chars_long',
    LOG_LEVEL: 'error',
    ENABLE_REQUEST_LOGGING: 'false',
    RATE_LIMIT_REQUESTS_PER_MINUTE: '60',
    RATE_LIMIT_BURST_SIZE: '10',
    CUSTOMER_REGISTRATION_FLOW_ID: 'test_reg_flow',
    PAYMENT_FLOW_ID: 'test_payment_flow',
    DEFAULT_CURRENCY: 'USD',
    PAYMENT_TIMEOUT_MINUTES: '15',
    SESSION_TIMEOUT_MINUTES: '30',
  };

  // Make environment available globally
  (globalThis as any).mockEnv = mockEnv;
  (globalThis as any).env = mockEnv;

  // Mock Cloudflare Workers Request/Response if not available
  if (typeof globalThis.Request === 'undefined') {
    (globalThis as any).Request = class MockRequest {
      constructor(public url: string, public init?: RequestInit) {}
      headers = new Map();
      method = 'GET';
      body = null;
      json = vi.fn().mockResolvedValue({});
      text = vi.fn().mockResolvedValue('');
      formData = vi.fn().mockResolvedValue(new FormData());
    };
  }

  if (typeof globalThis.Response === 'undefined') {
    (globalThis as any).Response = class MockResponse {
      constructor(public body?: any, public init?: ResponseInit) {}
      status = 200;
      statusText = 'OK';
      headers = new Map();
      json = vi.fn().mockResolvedValue({});
      text = vi.fn().mockResolvedValue('');
    };
  }

  if (typeof globalThis.Headers === 'undefined') {
    (globalThis as any).Headers = Map;
  }

  // Mock crypto for Node.js
  if (typeof globalThis.crypto === 'undefined') {
    const crypto = require('crypto');
    (globalThis as any).crypto = {
      randomUUID: () => crypto.randomUUID(),
      getRandomValues: (array: any) => crypto.getRandomValues(array),
      subtle: crypto.webcrypto?.subtle,
    };
  }

  // Mock fetch if not available
  if (typeof globalThis.fetch === 'undefined') {
    (globalThis as any).fetch = vi.fn().mockResolvedValue(
      new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      })
    );
  }
}

// Export mock environment for tests
export const mockEnv = (globalThis as any).mockEnv;

// Helper function to reset all mocks
export function resetAllMocks() {
  vi.clearAllMocks();
  server.resetHandlers();
}

// Helper function to create mock request
export function createMockRequest(url: string, options: RequestInit = {}) {
  return new Request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  });
}

// Helper function to create mock response
export function createMockResponse(body: any, status = 200) {
  return new Response(JSON.stringify(body), {
    status,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
