{"description": "WhatsApp Flow response webhook payload", "method": "POST", "url": "/webhook/whatsapp", "headers": {"Content-Type": "application/json", "X-Hub-Signature-256": "sha256=signature_here"}, "body": {"object": "whatsapp_business_account", "entry": [{"id": "WHATSAPP_BUSINESS_ACCOUNT_ID", "changes": [{"value": {"messaging_product": "whatsapp", "metadata": {"display_phone_number": "***********", "phone_number_id": "PHONE_NUMBER_ID"}, "contacts": [{"profile": {"name": "<PERSON>"}, "wa_id": "***********"}], "messages": [{"from": "***********", "id": "wamid.HBgLMTU1NTEyMzQ1NjcVAgASGBQzQTdBNjc4OUJDNTU4MjY4OTVFQQA=", "timestamp": "**********", "type": "interactive", "interactive": {"type": "nfm_reply", "nfm_reply": {"response_json": "{\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"+***********\"}", "body": "Registration completed", "name": "flow_response"}}}]}, "field": "messages"}]}]}, "expectedResponse": {"status": 200, "body": "OK"}}