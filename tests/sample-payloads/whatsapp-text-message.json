{"description": "WhatsApp text message webhook payload", "method": "POST", "url": "/webhook/whatsapp", "headers": {"Content-Type": "application/json", "X-Hub-Signature-256": "sha256=signature_here"}, "body": {"object": "whatsapp_business_account", "entry": [{"id": "WHATSAPP_BUSINESS_ACCOUNT_ID", "changes": [{"value": {"messaging_product": "whatsapp", "metadata": {"display_phone_number": "***********", "phone_number_id": "PHONE_NUMBER_ID"}, "contacts": [{"profile": {"name": "<PERSON>"}, "wa_id": "***********"}], "messages": [{"from": "***********", "id": "wamid.HBgLMTU1NTEyMzQ1NjcVAgASGBQzQTdBNjc4OUJDNTU4MjY4OTVFQQA=", "timestamp": "**********", "text": {"body": "Hello, I want to register"}, "type": "text"}]}, "field": "messages"}]}]}, "expectedResponse": {"status": 200, "body": "OK"}}