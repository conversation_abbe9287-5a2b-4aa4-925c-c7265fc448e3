// CI-specific test setup that doesn't use Cloudflare Workers runtime
// This avoids the node:inspector issue when running coverage

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';

// Mock Cloudflare Workers environment for CI
beforeAll(() => {
  // Mock global objects that would be available in Workers runtime
  global.Request = global.Request || class MockRequest {};
  global.Response = global.Response || class MockResponse {};
  global.Headers = global.Headers || class MockHeaders {};
  global.URL = global.URL || class MockURL {};
  global.URLSearchParams = global.URLSearchParams || class MockURLSearchParams {};
  
  // Mock crypto for CI environment
  if (!global.crypto) {
    global.crypto = {
      randomUUID: () => 'test-uuid-' + Math.random().toString(36).substr(2, 9),
      getRandomValues: (array: any) => {
        for (let i = 0; i < array.length; i++) {
          array[i] = Math.floor(Math.random() * 256);
        }
        return array;
      },
      subtle: {} as any,
    } as any;
  }

  // Mock environment variables
  process.env.NODE_ENV = 'test';
  process.env.ENVIRONMENT = 'test';
});

afterAll(() => {
  // Cleanup if needed
});

beforeEach(() => {
  // Reset mocks before each test
});

afterEach(() => {
  // Cleanup after each test
});

// Export mock factories for tests
export const createMockEnv = () => ({
  DB: {
    prepare: () => ({
      bind: () => ({
        all: () => Promise.resolve([]),
        first: () => Promise.resolve(null),
        run: () => Promise.resolve({ success: true, changes: 0 }),
      }),
    }),
    batch: () => Promise.resolve([]),
  },
  KV: {
    get: () => Promise.resolve(null),
    put: () => Promise.resolve(),
    delete: () => Promise.resolve(),
    list: () => Promise.resolve({ keys: [] }),
  },
  WHATSAPP_ACCESS_TOKEN: 'test-token',
  WHATSAPP_PHONE_NUMBER_ID: 'test-phone-id',
  WHATSAPP_WEBHOOK_VERIFY_TOKEN: 'test-verify-token',
  DPO_COMPANY_TOKEN: 'test-dpo-token',
  DPO_SERVICE_TYPE: 'test-service',
  ENVIRONMENT: 'test',
});

export const createMockLogger = () => ({
  info: () => {},
  warn: () => {},
  error: () => {},
  debug: () => {},
});
