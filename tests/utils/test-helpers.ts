import { vi } from 'vitest';
import { Container } from '@/shared/container/Container';
import { createAppConfig } from '@/shared/config/AppConfig';
import { CryptoUtils } from '@/shared/utils/crypto';
import { Logger } from '@/shared/utils/logger';
import { mockEnv, testDb } from '../setup';
import type { CloudflareBindings } from '@/shared/types/environment';
import { users, payments, flowSessions, auditLogs, rateLimits } from '@/infrastructure/database/schema';

/**
 * Create a test container with mocked dependencies
 */
export function createTestContainer(): Container {
  const container = new Container();
  
  // Mock configuration
  const config = createAppConfig(mockEnv as CloudflareBindings);
  container.register('config', config);
  
  // Mock utilities
  container.register('crypto_utils', new CryptoUtils(config.app.encryptionKey));
  container.register('logger', new Logger('error')); // Quiet logging in tests
  
  // Mock database connection
  container.register('database_connection', testDb);
  
  return container;
}

/**
 * Create mock Context for Hono tests
 */
export function createMockContext(overrides: any = {}) {
  const defaultReq = {
    method: 'GET',
    url: 'https://test.workers.dev/',
    header: vi.fn().mockReturnValue(undefined),
    query: vi.fn().mockReturnValue(undefined),
    param: vi.fn().mockReturnValue(undefined),
    json: vi.fn().mockResolvedValue({}),
    text: vi.fn().mockResolvedValue(''),
  };

  const mockContext = {
    req: {
      ...defaultReq,
      ...(overrides.req || {}),
      // Ensure header function is always available, even if overrides.req doesn't include it
      header: (overrides.req && overrides.req.header) ? overrides.req.header : defaultReq.header,
    },
    res: {
      status: 200,
      headers: new Map(),
    },
    env: mockEnv,
    get: vi.fn().mockImplementation((key: string) => {
      if (key === 'container') {
        return createTestContainer();
      }
      return undefined;
    }),
    set: vi.fn(),
    header: vi.fn(),
    text: vi.fn().mockImplementation((text: string, status?: number) => {
      return new Response(text, { status: status || 200 });
    }),
    json: vi.fn().mockImplementation((obj: any, status?: number) => {
      // Handle circular references in JSON serialization
      const safeStringify = (obj: any) => {
        const seen = new Set();
        return JSON.stringify(obj, (key, value) => {
          if (typeof value === 'object' && value !== null) {
            if (seen.has(value)) {
              return '[Circular]';
            }
            seen.add(value);
          }
          return value;
        });
      };

      return new Response(safeStringify(obj), {
        status: status || 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }),
    html: vi.fn().mockImplementation((html: string, status?: number) => {
      return new Response(html, {
        status: status || 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }),
    ...overrides,
  };

  return mockContext;
}

/**
 * Create mock Next function for middleware tests
 */
export function createMockNext() {
  return vi.fn().mockResolvedValue(undefined);
}

/**
 * Create mock Logger for testing
 */
export function createMockLogger() {
  return {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  };
}

/**
 * Create mock CryptoUtils for testing
 */
export function createMockCryptoUtils() {
  return {
    encrypt: vi.fn().mockImplementation((data: string) => `encrypted_${data}`),
    decrypt: vi.fn().mockImplementation((data: string) => data.replace('encrypted_', '')),
    hash: vi.fn().mockImplementation((data: string) => `hashed_${data}`),
    generateToken: vi.fn().mockReturnValue('mock_token_123'),
    generateId: vi.fn().mockReturnValue('mock_id_123'),
    generateUUID: vi.fn().mockReturnValue('mock_uuid_123'),
  };
}

/**
 * Helper to wait for async operations
 */
export function waitFor(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate test data with realistic values
 */
export class TestDataGenerator {
  private static counter = 0;

  static generateId(): string {
    return `test-id-${++this.counter}-${Date.now()}`;
  }

  static generatePhoneNumber(): string {
    const numbers = ['1234567890', '9876543210', '5555555555', '1111111111'];
    return `+${numbers[this.counter % numbers.length]}`;
  }

  static generateEmail(): string {
    const domains = ['example.com', 'test.com', 'demo.org'];
    return `user${++this.counter}@${domains[this.counter % domains.length]}`;
  }

  static generateName(): string {
    const names = ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown'];
    return names[this.counter % names.length];
  }

  static generateAmount(): number {
    const amounts = [29.99, 99.99, 199.99, 299.99];
    return amounts[this.counter % amounts.length];
  }

  static generateProductName(): string {
    const products = ['Basic Plan', 'Premium Plan', 'Enterprise Plan', 'Starter Pack'];
    return products[this.counter % products.length];
  }
}

/**
 * Mock external API responses
 */
export const mockApiResponses = {
  whatsapp: {
    sendMessage: {
      messaging_product: 'whatsapp',
      contacts: [{ input: '+1234567890', wa_id: '1234567890' }],
      messages: [{ id: 'wamid.test123' }],
    },
    error: {
      error: {
        message: 'Invalid phone number',
        type: 'OAuthException',
        code: 100,
      },
    },
  },
  dpo: {
    createToken: {
      Result: '000',
      ResultExplanation: 'Transaction created successfully',
      TransToken: 'TEST_TOKEN_123',
      TransRef: 'TEST_REF_123',
    },
    verifyToken: {
      Result: '000',
      ResultExplanation: 'Transaction verified successfully',
      TransactionStatus: '1',
      TransactionAmount: '99.99',
      TransactionCurrency: 'USD',
      CustomerEmail: '<EMAIL>',
      CustomerPhone: '+1234567890',
      TransactionRef: 'TEST_REF_123',
    },
    error: {
      Result: '001',
      ResultExplanation: 'Invalid company token',
    },
  },
};

/**
 * Assert helper functions
 */
export const assertions = {
  /**
   * Assert that a function throws with a specific message
   */
  async assertThrows(fn: () => Promise<any>, expectedMessage?: string): Promise<Error> {
    try {
      await fn();
      throw new Error('Expected function to throw');
    } catch (error) {
      if (expectedMessage && error instanceof Error) {
        if (!error.message.includes(expectedMessage)) {
          throw new Error(`Expected error message to contain "${expectedMessage}", got "${error.message}"`);
        }
      }
      return error as Error;
    }
  },

  /**
   * Assert that an object has specific properties
   */
  assertHasProperties(obj: any, properties: string[]): void {
    for (const prop of properties) {
      if (!(prop in obj)) {
        throw new Error(`Expected object to have property "${prop}"`);
      }
    }
  },

  /**
   * Assert that a response has correct structure
   */
  assertValidResponse(response: Response, expectedStatus: number): void {
    if (response.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus}, got ${response.status}`);
    }
  },
};

/**
 * Database test helpers
 */
export const dbHelpers = {
  /**
   * Insert test user into database
   */
  async insertTestUser(userData: any = {}) {
    const user = {
      id: TestDataGenerator.generateId(),
      whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
      name: TestDataGenerator.generateName(),
      email: TestDataGenerator.generateEmail(),
      registrationStatus: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...userData,
    };

    await testDb.insert(users).values(user);
    return user;
  },

  /**
   * Insert test payment into database
   */
  async insertTestPayment(paymentData: any = {}) {
    const payment = {
      id: TestDataGenerator.generateId(),
      userId: 'test-user-1',
      dpoTransactionToken: `TOKEN_${TestDataGenerator.generateId()}`,
      amount: TestDataGenerator.generateAmount(),
      currency: 'USD',
      status: 'pending',
      productName: TestDataGenerator.generateProductName(),
      customerEmail: TestDataGenerator.generateEmail(),
      customerPhone: TestDataGenerator.generatePhoneNumber(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...paymentData,
    };

    await testDb.insert(payments).values(payment);
    return payment;
  },

  /**
   * Insert test flow session into database
   */
  async insertTestFlowSession(sessionData: any = {}) {
    const session = {
      id: TestDataGenerator.generateId(),
      userId: 'test-user-1',
      flowType: 'customer_registration',
      flowId: 'test-flow-1',
      status: 'active',
      currentStep: 'start',
      sessionData: JSON.stringify({}),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
      ...sessionData,
    };

    await testDb.insert(flowSessions).values(session);
    return session;
  },

  /**
   * Clear all test data
   */
  async clearAllData() {
    await testDb.delete(rateLimits);
    await testDb.delete(auditLogs);
    await testDb.delete(flowSessions);
    await testDb.delete(payments);
    await testDb.delete(users);
  },
};
