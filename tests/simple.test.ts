import { describe, it, expect } from 'vitest';
import { env } from 'cloudflare:test';

describe('Cloudflare Workers Test Setup Verification', () => {
  it('should run basic assertions', () => {
    expect(1 + 1).toBe(2);
    expect('hello').toBe('hello');
    expect(true).toBe(true);
  });

  it('should handle async operations', async () => {
    const result = await Promise.resolve('async result');
    expect(result).toBe('async result');
  });

  it('should work with objects', () => {
    const obj = { name: 'test', value: 42 };
    expect(obj).toEqual({ name: 'test', value: 42 });
    expect(obj).toHaveProperty('name');
    expect(obj.name).toBe('test');
  });

  it('should work with arrays', () => {
    const arr = [1, 2, 3];
    expect(arr).toHaveLength(3);
    expect(arr).toContain(2);
    expect(arr[0]).toBe(1);
  });

  it('should handle errors', () => {
    expect(() => {
      throw new Error('Test error');
    }).toThrow('Test error');
  });

  it('should have access to Cloudflare Workers environment', () => {
    expect(env).toBeDefined();
    expect(typeof env).toBe('object');
  });

  it('should have access to test environment variables', () => {
    expect(env.ENVIRONMENT).toBeDefined();
    expect(env.WHATSAPP_VERIFY_TOKEN).toBeDefined();
    expect(env.DPO_COMPANY_TOKEN).toBeDefined();
  });

  it('should have access to test database', async () => {
    expect(env.TEST_DB).toBeDefined();

    // Test basic database operation
    try {
      const result = await env.TEST_DB.prepare('SELECT 1 as test').first();
      expect(result?.test).toBe(1);
    } catch (error) {
      console.warn('Database test skipped:', error);
      // Don't fail the test if database is not available
    }
  });

  it('should have access to test KV namespace', () => {
    expect(env.TEST_KV).toBeDefined();
    expect(typeof env.TEST_KV).toBe('object');
  });
});
