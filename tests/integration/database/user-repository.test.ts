import { describe, it, expect, beforeEach } from 'vitest';
import { UserRepository } from '@/infrastructure/repositories/UserRepository';
import { CryptoUtils } from '@/shared/utils/crypto';
import { testDb, createTestUser } from '@tests/setup';
import { users } from '@/infrastructure/database/schema';
import { TestDataGenerator } from '@tests/utils/test-helpers';

describe('UserRepository Integration', () => {
  let userRepository: UserRepository;
  let cryptoUtils: CryptoUtils;

  beforeEach(async () => {
    cryptoUtils = new CryptoUtils('test_encryption_key_32_chars_long');
    userRepository = new UserRepository(testDb, cryptoUtils);
    
    // Clean database
    await testDb.delete(users);
  });

  describe('create', () => {
    it('should create a new user successfully', async () => {
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: TestDataGenerator.generateName(),
        email: TestDataGenerator.generateEmail(),
      };

      const result = await userRepository.create(userData);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.whatsappPhoneNumber).toBe(userData.whatsappPhoneNumber);
      expect(result.name).toBe(userData.name);
      expect(result.email).toBe(userData.email);
      expect(result.registrationStatus).toBe('pending');
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('should create user with minimal data', async () => {
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
      };

      const result = await userRepository.create(userData);

      expect(result).toBeDefined();
      expect(result.whatsappPhoneNumber).toBe(userData.whatsappPhoneNumber);
      expect(result.name).toBeUndefined();
      expect(result.email).toBeUndefined();
      expect(result.registrationStatus).toBe('pending');
    });

    it('should throw error for duplicate phone number', async () => {
      const phoneNumber = TestDataGenerator.generatePhoneNumber();
      
      await userRepository.create({
        whatsappPhoneNumber: phoneNumber,
        name: 'First User',
      });

      await expect(userRepository.create({
        whatsappPhoneNumber: phoneNumber,
        name: 'Second User',
      })).rejects.toThrow();
    });
  });

  describe('findById', () => {
    it('should find user by ID', async () => {
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: TestDataGenerator.generateName(),
        email: TestDataGenerator.generateEmail(),
      };

      const createdUser = await userRepository.create(userData);
      const foundUser = await userRepository.findById(createdUser.id);

      expect(foundUser).toBeDefined();
      expect(foundUser?.id).toBe(createdUser.id);
      expect(foundUser?.whatsappPhoneNumber).toBe(userData.whatsappPhoneNumber);
    });

    it('should return null for non-existent ID', async () => {
      const result = await userRepository.findById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('findByWhatsAppPhone', () => {
    it('should find user by WhatsApp phone number', async () => {
      const phoneNumber = TestDataGenerator.generatePhoneNumber();
      const userData = {
        whatsappPhoneNumber: phoneNumber,
        name: TestDataGenerator.generateName(),
        email: TestDataGenerator.generateEmail(),
      };

      await userRepository.create(userData);
      const foundUser = await userRepository.findByWhatsAppPhone(phoneNumber);

      expect(foundUser).toBeDefined();
      expect(foundUser?.whatsappPhoneNumber).toBe(phoneNumber);
    });

    it('should return null for non-existent phone number', async () => {
      const result = await userRepository.findByWhatsAppPhone('+9999999999');
      expect(result).toBeNull();
    });
  });

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      const email = TestDataGenerator.generateEmail();
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: TestDataGenerator.generateName(),
        email,
      };

      await userRepository.create(userData);
      const foundUser = await userRepository.findByEmail(email);

      expect(foundUser).toBeDefined();
      expect(foundUser?.email).toBe(email);
    });

    it('should return null for non-existent email', async () => {
      const result = await userRepository.findByEmail('<EMAIL>');
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: 'Original Name',
        email: '<EMAIL>',
      };

      const createdUser = await userRepository.create(userData);

      // Add a small delay to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      const updateData = {
        name: 'Updated Name',
        email: '<EMAIL>',
        registrationStatus: 'completed' as const,
      };

      const updatedUser = await userRepository.update(createdUser.id, updateData);

      expect(updatedUser).toBeDefined();
      expect(updatedUser?.name).toBe(updateData.name);
      expect(updatedUser?.email).toBe(updateData.email);
      expect(updatedUser?.registrationStatus).toBe(updateData.registrationStatus);
      expect(new Date(updatedUser!.updatedAt).getTime()).toBeGreaterThanOrEqual(
        new Date(createdUser.updatedAt).getTime()
      );
    });

    it('should return null for non-existent user', async () => {
      const result = await userRepository.update('non-existent-id', {
        name: 'Updated Name',
      });

      expect(result).toBeNull();
    });

    it('should update partial data', async () => {
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: 'Original Name',
        email: '<EMAIL>',
      };

      const createdUser = await userRepository.create(userData);
      
      const updatedUser = await userRepository.update(createdUser.id, {
        name: 'Updated Name Only',
      });

      expect(updatedUser).toBeDefined();
      expect(updatedUser?.name).toBe('Updated Name Only');
      expect(updatedUser?.email).toBe(userData.email); // Should remain unchanged
    });
  });

  describe('delete', () => {
    it('should delete user successfully', async () => {
      const userData = {
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: TestDataGenerator.generateName(),
      };

      const createdUser = await userRepository.create(userData);
      const deleteResult = await userRepository.delete(createdUser.id);

      expect(deleteResult).toBe(true);

      const foundUser = await userRepository.findById(createdUser.id);
      expect(foundUser).toBeNull();
    });

    it('should return false for non-existent user', async () => {
      const result = await userRepository.delete('non-existent-id');
      expect(result).toBe(false);
    });
  });

  describe('exists', () => {
    it('should return true for existing phone number', async () => {
      const phoneNumber = TestDataGenerator.generatePhoneNumber();
      
      await userRepository.create({
        whatsappPhoneNumber: phoneNumber,
        name: TestDataGenerator.generateName(),
      });

      const exists = await userRepository.exists(phoneNumber);
      expect(exists).toBe(true);
    });

    it('should return false for non-existent phone number', async () => {
      const exists = await userRepository.exists('+9999999999');
      expect(exists).toBe(false);
    });
  });

  describe('getRegistrationStats', () => {
    it('should return correct registration statistics', async () => {
      // Create users with different statuses
      await userRepository.create({
        whatsappPhoneNumber: '+1111111111',
        name: 'User 1',
        email: '<EMAIL>',
      });

      const user2 = await userRepository.create({
        whatsappPhoneNumber: '+2222222222',
        name: 'User 2',
      });

      const user3 = await userRepository.create({
        whatsappPhoneNumber: '+3333333333',
        name: 'User 3',
      });

      // Update statuses
      await userRepository.update(user2.id, { registrationStatus: 'completed' });
      await userRepository.update(user3.id, { registrationStatus: 'failed' });

      const stats = await userRepository.getRegistrationStats();

      expect(stats.total).toBe(3);
      expect(stats.pending).toBe(1);
      expect(stats.completed).toBe(1);
      expect(stats.failed).toBe(1);
    });

    it('should return zero stats for empty database', async () => {
      const stats = await userRepository.getRegistrationStats();

      expect(stats.total).toBe(0);
      expect(stats.pending).toBe(0);
      expect(stats.completed).toBe(0);
      expect(stats.failed).toBe(0);
    });
  });

  describe('concurrent operations', () => {
    it('should handle concurrent user creation', async () => {
      const promises = Array.from({ length: 5 }, (_, i) => 
        userRepository.create({
          whatsappPhoneNumber: `+111111111${i}`,
          name: `User ${i}`,
        })
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach((user, index) => {
        expect(user.whatsappPhoneNumber).toBe(`+111111111${index}`);
        expect(user.name).toBe(`User ${index}`);
      });
    });

    it('should handle concurrent updates to same user', async () => {
      const user = await userRepository.create({
        whatsappPhoneNumber: TestDataGenerator.generatePhoneNumber(),
        name: 'Original Name',
      });

      const promises = [
        userRepository.update(user.id, { name: 'Update 1' }),
        userRepository.update(user.id, { email: '<EMAIL>' }),
        userRepository.update(user.id, { registrationStatus: 'completed' }),
      ];

      const results = await Promise.all(promises);

      // All updates should succeed
      results.forEach(result => {
        expect(result).toBeDefined();
      });

      // Verify final state
      const finalUser = await userRepository.findById(user.id);
      expect(finalUser).toBeDefined();
    });
  });
});
