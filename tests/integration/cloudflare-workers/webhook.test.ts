import { describe, it, expect, beforeEach } from 'vitest';
import { env, SELF, createExecutionContext, waitOnExecutionContext } from 'cloudflare:test';

describe('WhatsApp Webhook Integration (Cloudflare Workers)', () => {
  beforeEach(async () => {
    // Clean up test data before each test
    await env.TEST_DB.exec(`
      DELETE FROM rate_limits;
      DELETE FROM audit_logs;
      DELETE FROM flow_sessions;
      DELETE FROM payments;
      DELETE FROM users;
    `);
  });

  describe('Webhook Verification', () => {
    it('should verify webhook with correct token', async () => {
      const verifyToken = env.WHATSAPP_VERIFY_TOKEN;
      const challenge = 'test_challenge_123';
      
      const response = await SELF.fetch(
        `https://example.com/api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=${verifyToken}&hub.challenge=${challenge}`,
        {
          method: 'GET',
        }
      );

      expect(response.status).toBe(200);
      expect(await response.text()).toBe(challenge);
    });

    it('should reject webhook with incorrect token', async () => {
      const wrongToken = 'wrong_token';
      const challenge = 'test_challenge_123';
      
      const response = await SELF.fetch(
        `https://example.com/api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=${wrongToken}&hub.challenge=${challenge}`,
        {
          method: 'GET',
        }
      );

      expect(response.status).toBe(403);
    });

    it('should reject webhook without required parameters', async () => {
      const response = await SELF.fetch(
        'https://example.com/api/whatsapp/webhook',
        {
          method: 'GET',
        }
      );

      expect(response.status).toBe(400);
    });
  });

  describe('Message Processing', () => {
    it('should process text message webhook', async () => {
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: env.WHATSAPP_PHONE_NUMBER_ID,
                  },
                  contacts: [
                    {
                      profile: {
                        name: 'Test User',
                      },
                      wa_id: '**********',
                    },
                  ],
                  messages: [
                    {
                      from: '**********',
                      id: 'message_id_123',
                      timestamp: '**********',
                      type: 'text',
                      text: {
                        body: 'hello',
                      },
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Hub-Signature-256': 'sha256=test_signature', // This would be properly calculated in real scenario
        },
        body: JSON.stringify(webhookPayload),
      });

      expect(response.status).toBe(200);
      
      // Verify user was created in database
      const users = await env.TEST_DB.prepare('SELECT * FROM users WHERE whatsapp_phone_number = ?')
        .bind('+**********')
        .all();
      
      expect(users.results).toHaveLength(1);
    });

    it('should process interactive message webhook', async () => {
      // First create a user
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, name, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+**********',
        'Test User',
        'completed',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: env.WHATSAPP_PHONE_NUMBER_ID,
                  },
                  messages: [
                    {
                      from: '**********',
                      id: 'message_id_123',
                      timestamp: '**********',
                      type: 'interactive',
                      interactive: {
                        type: 'button_reply',
                        button_reply: {
                          id: 'register_btn',
                          title: 'Register',
                        },
                      },
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Hub-Signature-256': 'sha256=test_signature',
        },
        body: JSON.stringify(webhookPayload),
      });

      expect(response.status).toBe(200);
    });

    it('should handle flow response webhook', async () => {
      // First create a user and flow session
      await env.TEST_DB.prepare(`
        INSERT INTO users (id, whatsapp_phone_number, name, registration_status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        'user-123',
        '+**********',
        'Test User',
        'pending',
        new Date().toISOString(),
        new Date().toISOString()
      ).run();

      await env.TEST_DB.prepare(`
        INSERT INTO flow_sessions (id, user_id, flow_type, flow_id, status, current_step, session_data, created_at, updated_at, expires_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        'session-123',
        'user-123',
        'customer_registration',
        env.CUSTOMER_REGISTRATION_FLOW_ID,
        'active',
        'registration_form',
        '{}',
        new Date().toISOString(),
        new Date().toISOString(),
        new Date(Date.now() + 30 * 60 * 1000).toISOString()
      ).run();

      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: env.WHATSAPP_PHONE_NUMBER_ID,
                  },
                  messages: [
                    {
                      from: '**********',
                      id: 'message_id_123',
                      timestamp: '**********',
                      type: 'interactive',
                      interactive: {
                        type: 'nfm_reply',
                        nfm_reply: {
                          response_json: JSON.stringify({
                            name: 'John Doe',
                            email: '<EMAIL>',
                          }),
                          body: 'Registration completed',
                          name: 'registration_form',
                        },
                      },
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Hub-Signature-256': 'sha256=test_signature',
        },
        body: JSON.stringify(webhookPayload),
      });

      expect(response.status).toBe(200);
      
      // Verify user registration was completed
      const users = await env.TEST_DB.prepare('SELECT * FROM users WHERE id = ?')
        .bind('user-123')
        .first();
      
      expect(users?.registration_status).toBe('completed');
      expect(users?.name).toBe('John Doe');
      expect(users?.email).toBe('<EMAIL>');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const requests = [];
      const maxRequests = parseInt(env.RATE_LIMIT_REQUESTS_PER_MINUTE);
      
      // Make requests up to the limit
      for (let i = 0; i < maxRequests + 5; i++) {
        requests.push(
          SELF.fetch('https://example.com/api/whatsapp/webhook', {
            method: 'GET',
            headers: {
              'X-Forwarded-For': '192.168.1.1',
            },
          })
        );
      }
      
      const responses = await Promise.all(requests);
      
      // First requests should succeed
      expect(responses.slice(0, maxRequests).every(r => r.status !== 429)).toBe(true);
      
      // Additional requests should be rate limited
      expect(responses.slice(maxRequests).some(r => r.status === 429)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON payload', async () => {
      const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: 'invalid json',
      });

      expect(response.status).toBe(400);
    });

    it('should handle missing signature', async () => {
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [],
      };

      const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookPayload),
      });

      expect(response.status).toBe(401);
    });

    it('should handle database errors gracefully', async () => {
      // This test would simulate database connection issues
      // In a real scenario, you might mock the database to throw errors
      
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'entry_id',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '**********',
                    phone_number_id: env.WHATSAPP_PHONE_NUMBER_ID,
                  },
                  messages: [
                    {
                      from: '**********',
                      id: 'message_id_123',
                      timestamp: '**********',
                      type: 'text',
                      text: {
                        body: 'hello',
                      },
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      };

      const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Hub-Signature-256': 'sha256=test_signature',
        },
        body: JSON.stringify(webhookPayload),
      });

      // Should handle errors gracefully and return 200 to WhatsApp
      expect(response.status).toBe(200);
    });
  });
});
