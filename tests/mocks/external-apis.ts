import { vi } from 'vitest';
import { http, HttpResponse } from 'msw';

/**
 * Mock WhatsApp API responses
 */
export const whatsappApiMocks = [
  // Send message success
  http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', async ({ request, params }) => {
    const body = await request.json() as any;
    
    // Simulate different response based on request
    if (body.to === 'error_phone') {
      return HttpResponse.json({
        error: {
          message: 'Invalid phone number',
          type: 'OAuthException',
          code: 100,
        },
      }, { status: 400 });
    }

    return HttpResponse.json({
      messaging_product: 'whatsapp',
      contacts: [{ input: body.to, wa_id: body.to.replace('+', '') }],
      messages: [{ id: `wamid.test_${Date.now()}` }],
    });
  }),

  // Mark message as read
  http.post('https://graph.facebook.com/v18.0/:phoneNumberId/messages', async ({ request }) => {
    const body = await request.json() as any;
    
    if (body.status === 'read') {
      return HttpResponse.json({
        success: true,
      });
    }

    return HttpResponse.json({
      error: {
        message: 'Invalid request',
        type: 'OAuthException',
        code: 100,
      },
    }, { status: 400 });
  }),
];

/**
 * Mock DPO Pay API responses
 */
export const dpoApiMocks = [
  http.post('https://secure.3gdirectpay.com/payv2.php', async ({ request }) => {
    const body = await request.json() as any;
    
    // Create token request
    if (body.Request === 'createToken') {
      // Simulate error for specific company token
      if (body.CompanyToken === 'invalid_token') {
        return HttpResponse.json({
          Result: '001',
          ResultExplanation: 'Invalid company token',
        });
      }

      return HttpResponse.json({
        Result: '000',
        ResultExplanation: 'Transaction created successfully',
        TransToken: `TOKEN_${Date.now()}`,
        TransRef: `REF_${Date.now()}`,
      });
    }

    // Verify token request
    if (body.Request === 'verifyToken') {
      // Simulate different statuses based on token
      if (body.TransactionToken === 'failed_token') {
        return HttpResponse.json({
          Result: '000',
          ResultExplanation: 'Transaction verified',
          TransactionStatus: '2', // Failed
          TransactionAmount: '99.99',
          TransactionCurrency: 'USD',
          CustomerEmail: '<EMAIL>',
          CustomerPhone: '+1234567890',
          TransactionRef: 'REF_FAILED',
        });
      }

      if (body.TransactionToken === 'cancelled_token') {
        return HttpResponse.json({
          Result: '000',
          ResultExplanation: 'Transaction verified',
          TransactionStatus: '4', // Cancelled
          TransactionAmount: '99.99',
          TransactionCurrency: 'USD',
          CustomerEmail: '<EMAIL>',
          CustomerPhone: '+1234567890',
          TransactionRef: 'REF_CANCELLED',
        });
      }

      return HttpResponse.json({
        Result: '000',
        ResultExplanation: 'Transaction verified successfully',
        TransactionStatus: '1', // Completed
        TransactionAmount: '99.99',
        TransactionCurrency: 'USD',
        CustomerEmail: '<EMAIL>',
        CustomerPhone: '+1234567890',
        TransactionRef: 'REF_SUCCESS',
        TransactionSettlementDate: new Date().toISOString().split('T')[0],
      });
    }

    // Cancel token request
    if (body.Request === 'cancelToken') {
      return HttpResponse.json({
        Result: '000',
        ResultExplanation: 'Transaction cancelled successfully',
      });
    }

    // Get payment methods request
    if (body.Request === 'getPaymentMethods') {
      return HttpResponse.json({
        Result: '000',
        ResultExplanation: 'Payment methods retrieved successfully',
        PaymentMethods: [
          {
            id: 'card',
            name: 'Credit/Debit Card',
            description: 'Visa, Mastercard, American Express',
          },
          {
            id: 'mobile',
            name: 'Mobile Money',
            description: 'M-Pesa, Airtel Money, etc.',
          },
        ],
      });
    }

    // Unknown request
    return HttpResponse.json({
      Result: '001',
      ResultExplanation: 'Unknown request type',
    });
  }),
];

/**
 * Mock service classes for unit testing
 */
export const createMockWhatsAppAdapter = () => ({
  sendTextMessage: vi.fn().mockResolvedValue({ messageId: 'test_message_id' }),
  sendInteractiveMessage: vi.fn().mockResolvedValue({ messageId: 'test_message_id' }),
  sendFlowMessage: vi.fn().mockResolvedValue({ messageId: 'test_message_id' }),
  markMessageAsRead: vi.fn().mockResolvedValue(undefined),
});

export const createMockDpoAdapter = () => ({
  createPaymentToken: vi.fn().mockResolvedValue({
    success: true,
    reference: 'TEST_REF_123',
    paymentUrl: 'https://secure.3gdirectpay.com/?ID=TEST_TOKEN_123',
    transactionToken: 'TEST_TOKEN_123',
  }),
  verifyPayment: vi.fn().mockResolvedValue({
    success: true,
    status: 'completed',
    amount: 99.99,
    currency: 'USD',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    transactionToken: 'TEST_TOKEN_123',
    reference: 'TEST_REF_123',
  }),
  cancelPayment: vi.fn().mockResolvedValue(true),
  getPaymentMethods: vi.fn().mockResolvedValue([
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, American Express',
    },
  ]),
});

export const createMockUserRepository = () => ({
  create: vi.fn(),
  findById: vi.fn(),
  findByWhatsAppPhone: vi.fn(),
  findByEmail: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  exists: vi.fn(),
  getRegistrationStats: vi.fn(),
});

export const createMockPaymentRepository = () => ({
  create: vi.fn(),
  findById: vi.fn(),
  findByDpoToken: vi.fn(),
  findByDpoReference: vi.fn(),
  findByUserId: vi.fn(),
  findByStatus: vi.fn(),
  update: vi.fn(),
  updateByDpoToken: vi.fn(),
  getPaymentStats: vi.fn(),
  findExpiredPayments: vi.fn(),
});

export const createMockFlowSessionRepository = () => ({
  create: vi.fn(),
  findById: vi.fn(),
  findActiveByUserId: vi.fn(),
  findActiveByUserIdAndType: vi.fn(),
  findByFlowId: vi.fn(),
  update: vi.fn(),
  updateSessionData: vi.fn(),
  expireSession: vi.fn(),
  findExpiredSessions: vi.fn(),
  cleanupExpiredSessions: vi.fn(),
  getSessionStats: vi.fn(),
});

export const createMockLogger = () => ({
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
});

export const createMockCryptoUtils = () => ({
  encrypt: vi.fn().mockReturnValue('encrypted_data'),
  decrypt: vi.fn().mockReturnValue('decrypted_data'),
  generateRandomString: vi.fn().mockReturnValue('random_string'),
  createHmacSignature: vi.fn().mockReturnValue('signature'),
  verifyHmacSignature: vi.fn().mockReturnValue(true),
  secureCompare: vi.fn().mockReturnValue(true),
  hash: vi.fn().mockReturnValue('hashed_data'),
  generateUUID: vi.fn().mockReturnValue('test-uuid-123'),
});

// Mock factory for UserService
export const createMockUserService = () => ({
  createUser: vi.fn().mockResolvedValue({
    id: 'user-123',
    whatsappPhoneNumber: '+1234567890',
    registrationStatus: 'pending',
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  getUserById: vi.fn().mockResolvedValue(null),
  getUserByWhatsAppPhone: vi.fn().mockResolvedValue(null),
  getUserByEmail: vi.fn().mockResolvedValue(null),
  updateUser: vi.fn().mockResolvedValue(null),
  completeUserRegistration: vi.fn().mockResolvedValue({
    id: 'user-123',
    whatsappPhoneNumber: '+1234567890',
    name: 'John Doe',
    email: '<EMAIL>',
    registrationStatus: 'completed',
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  userExists: vi.fn().mockResolvedValue(false),
  getRegistrationStats: vi.fn().mockResolvedValue({
    total: 100,
    completed: 85,
    pending: 10,
    failed: 5,
  }),
});

// Mock factory for PaymentService
export const createMockPaymentService = () => ({
  createPayment: vi.fn().mockResolvedValue({
    id: 'payment-123',
    userId: 'user-123',
    amount: 99.99,
    currency: 'USD',
    status: 'pending',
    productName: 'Test Product',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  initiatePayment: vi.fn().mockResolvedValue({
    paymentUrl: 'https://secure.3gdirectpay.com/?ID=TOKEN_123',
    payment: {
      id: 'payment-123',
      status: 'initiated',
      dpoPaymentReference: 'DPO_REF_123',
      paymentUrl: 'https://secure.3gdirectpay.com/?ID=TOKEN_123',
    },
  }),
  updatePaymentStatus: vi.fn().mockResolvedValue({
    id: 'payment-123',
    status: 'completed',
    completedAt: new Date(),
  }),
  handlePaymentCallback: vi.fn().mockResolvedValue(null),
  getPaymentById: vi.fn().mockResolvedValue(null),
  getPaymentsByUserId: vi.fn().mockResolvedValue([]),
  getPaymentStats: vi.fn().mockResolvedValue({
    total: 100,
    completed: 85,
    pending: 10,
    failed: 5,
    totalAmount: 9999.99,
    completedAmount: 8499.99,
  }),
  getPaymentByDpoToken: vi.fn().mockResolvedValue(null),
  processExpiredPayments: vi.fn().mockResolvedValue(5),
});

// Mock factory for FlowService
export const createMockFlowService = () => ({
  startCustomerRegistrationFlow: vi.fn().mockResolvedValue({
    id: 'session-123',
    userId: 'user-123',
    flowType: 'customer_registration',
    flowId: 'flow-123',
    status: 'active',
    currentStep: 'start',
    sessionData: {},
    expiresAt: new Date(Date.now() + 30 * 60 * 1000),
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  startPaymentFlow: vi.fn().mockResolvedValue({
    id: 'session-123',
    userId: 'user-123',
    flowType: 'payment',
    flowId: 'payment-flow-123',
    status: 'active',
    currentStep: 'product_selection',
    sessionData: { products: [] },
    expiresAt: new Date(Date.now() + 30 * 60 * 1000),
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  processFlowResponse: vi.fn().mockResolvedValue(undefined),
  processFlowInput: vi.fn().mockResolvedValue(undefined),
  selectProduct: vi.fn().mockResolvedValue(undefined),
  getActiveSession: vi.fn().mockResolvedValue(null),
  expireOldSessions: vi.fn().mockResolvedValue(5),
});

// Mock factory for WhatsAppService
export const createMockWhatsAppService = () => ({
  handleIncomingMessage: vi.fn().mockResolvedValue(undefined),
  handleMessageStatus: vi.fn().mockResolvedValue(undefined),
});

// Test data generator utility
export class TestDataGenerator {
  static generateId(): string {
    return `test-${Math.random().toString(36).substr(2, 9)}`;
  }

  static generatePhoneNumber(): string {
    const numbers = ['1234567890', '9876543210', '5555555555', '1111111111'];
    return `+${numbers[Math.floor(Math.random() * numbers.length)]}`;
  }

  static generateEmail(): string {
    const domains = ['example.com', 'test.com', 'demo.org'];
    const names = ['john', 'jane', 'test', 'user', 'demo'];
    const name = names[Math.floor(Math.random() * names.length)];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    return `${name}@${domain}`;
  }

  static generateName(): string {
    const firstNames = ['John', 'Jane', 'Bob', 'Alice', 'Charlie'];
    const lastNames = ['Doe', 'Smith', 'Johnson', 'Brown', 'Davis'];
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    return `${firstName} ${lastName}`;
  }

  static generateAmount(): number {
    return Math.round((Math.random() * 1000 + 10) * 100) / 100;
  }

  static generateProductName(): string {
    const products = ['Basic Plan', 'Premium Plan', 'Enterprise Plan', 'Starter Package', 'Pro Package'];
    return products[Math.floor(Math.random() * products.length)];
  }
}

/**
 * Mock webhook payloads for testing
 */
export const mockWebhookPayloads = {
  whatsappVerification: {
    'hub.mode': 'subscribe',
    'hub.verify_token': 'test_verify_token',
    'hub.challenge': 'test_challenge_string',
  },

  whatsappTextMessage: {
    object: 'whatsapp_business_account',
    entry: [
      {
        id: 'WHATSAPP_BUSINESS_ACCOUNT_ID',
        changes: [
          {
            value: {
              messaging_product: 'whatsapp',
              metadata: {
                display_phone_number: '***********',
                phone_number_id: 'PHONE_NUMBER_ID',
              },
              contacts: [
                {
                  profile: { name: 'Test User' },
                  wa_id: '***********',
                },
              ],
              messages: [
                {
                  from: '***********',
                  id: 'test_message_id',
                  timestamp: '**********',
                  text: { body: 'hello' },
                  type: 'text',
                },
              ],
            },
            field: 'messages',
          },
        ],
      },
    ],
  },

  whatsappFlowResponse: {
    object: 'whatsapp_business_account',
    entry: [
      {
        id: 'WHATSAPP_BUSINESS_ACCOUNT_ID',
        changes: [
          {
            value: {
              messaging_product: 'whatsapp',
              metadata: {
                display_phone_number: '***********',
                phone_number_id: 'PHONE_NUMBER_ID',
              },
              messages: [
                {
                  from: '***********',
                  id: 'test_message_id',
                  timestamp: '**********',
                  type: 'interactive',
                  interactive: {
                    type: 'nfm_reply',
                    nfm_reply: {
                      response_json: JSON.stringify({
                        name: 'John Doe',
                        email: '<EMAIL>',
                        phone: '+***********',
                      }),
                      body: 'Registration completed',
                      name: 'flow_response',
                    },
                  },
                },
              ],
            },
            field: 'messages',
          },
        ],
      },
    ],
  },

  dpoPaymentCallback: {
    TransactionToken: 'TEST_TOKEN_123',
    CompanyRef: 'payment-123',
    Result: '000',
    ResultExplanation: 'Transaction Paid',
    TransactionRef: 'DPO123456',
    TransactionAmount: '99.99',
    TransactionCurrency: 'USD',
    CustomerEmail: '<EMAIL>',
    CustomerPhone: '+***********',
  },
};
