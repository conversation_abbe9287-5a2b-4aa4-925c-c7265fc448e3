#!/bin/bash

# WhatsApp Flow Application - Test Commands
# Make sure to replace YOUR_WORKER_URL with your actual Cloudflare Worker URL

BASE_URL="https://your-worker-domain.workers.dev"

echo "=== WhatsApp Flow Application Test Commands ==="
echo ""

echo "1. Health Check"
curl -X GET "$BASE_URL/" \
  -H "Content-Type: application/json" | jq .
echo ""

echo "2. WhatsApp Webhook Verification"
curl -X GET "$BASE_URL/webhook/whatsapp?hub.mode=subscribe&hub.verify_token=your_verify_token&hub.challenge=test_challenge" \
  -H "Content-Type: application/json"
echo ""

echo "3. WhatsApp Text Message Webhook"
curl -X POST "$BASE_URL/webhook/whatsapp" \
  -H "Content-Type: application/json" \
  -H "X-Hub-Signature-256: sha256=test_signature" \
  -d '{
    "object": "whatsapp_business_account",
    "entry": [
      {
        "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
        "changes": [
          {
            "value": {
              "messaging_product": "whatsapp",
              "metadata": {
                "display_phone_number": "***********",
                "phone_number_id": "PHONE_NUMBER_ID"
              },
              "contacts": [
                {
                  "profile": {
                    "name": "Test User"
                  },
                  "wa_id": "***********"
                }
              ],
              "messages": [
                {
                  "from": "***********",
                  "id": "test_message_id",
                  "timestamp": "**********",
                  "text": {
                    "body": "hello"
                  },
                  "type": "text"
                }
              ]
            },
            "field": "messages"
          }
        ]
      }
    ]
  }' | jq .
echo ""

echo "4. DPO Payment Callback (Success)"
curl -X POST "$BASE_URL/webhook/dpo" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "TransactionToken=TEST123&CompanyRef=payment-test&Result=000&ResultExplanation=Transaction+Paid&TransactionRef=DPO123&TransactionAmount=99.99&TransactionCurrency=USD&CustomerEmail=<EMAIL>&CustomerPhone=+***********"
echo ""

echo "5. Payment Methods API"
curl -X GET "$BASE_URL/api/payment/methods" \
  -H "Content-Type: application/json" | jq .
echo ""

echo "6. Payment Health Check"
curl -X GET "$BASE_URL/health/payment" \
  -H "Content-Type: application/json" | jq .
echo ""

echo "7. WhatsApp Webhook Info (Development Only)"
curl -X GET "$BASE_URL/webhook/whatsapp/info" \
  -H "Content-Type: application/json" | jq .
echo ""

echo "=== Rate Limiting Test ==="
echo "8. Test Rate Limiting (send multiple requests quickly)"
for i in {1..5}; do
  echo "Request $i:"
  curl -X GET "$BASE_URL/" \
    -H "Content-Type: application/json" \
    -w "Status: %{http_code}, Time: %{time_total}s\n" \
    -s -o /dev/null
done
echo ""

echo "=== Error Handling Test ==="
echo "9. Test 404 Error"
curl -X GET "$BASE_URL/nonexistent" \
  -H "Content-Type: application/json" | jq .
echo ""

echo "10. Test Invalid JSON"
curl -X POST "$BASE_URL/webhook/whatsapp" \
  -H "Content-Type: application/json" \
  -d '{"invalid": json}' | jq .
echo ""

echo "=== Security Headers Test ==="
echo "11. Check Security Headers"
curl -I "$BASE_URL/" | grep -E "(X-Content-Type-Options|X-Frame-Options|X-XSS-Protection|Strict-Transport-Security)"
echo ""

echo "All tests completed!"
echo ""
echo "Note: Replace 'your-worker-domain.workers.dev' with your actual Cloudflare Worker URL"
echo "Note: Update environment variables in .env file before testing"
