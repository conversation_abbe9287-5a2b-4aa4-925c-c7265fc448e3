#!/bin/bash

# Verify CI/CD Setup Script
# This script checks that all CI/CD components are properly configured

set -e

echo "🔧 Verifying CI/CD Setup for WhatsApp Flow Application"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: package.json not found. Please run this script from the project root.${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Checking Project Configuration...${NC}"

# 1. Check package.json configuration
echo ""
echo "1. Package.json Configuration"
echo "-----------------------------"

# Check packageManager field
if grep -q '"packageManager".*"pnpm@9.0.0"' package.json; then
    print_status 0 "packageManager field correctly set to pnpm@9.0.0"
else
    print_status 1 "packageManager field missing or incorrect"
fi

# Check required scripts
required_scripts=("test" "test:coverage" "test:node" "lint" "type-check" "build" "deploy")
for script in "${required_scripts[@]}"; do
    if grep -q "\"$script\":" package.json; then
        print_status 0 "Script '$script' found"
    else
        print_status 1 "Script '$script' missing"
    fi
done

# 2. Check GitHub Actions workflow
echo ""
echo "2. GitHub Actions Workflow"
echo "--------------------------"

if [ -f ".github/workflows/ci.yml" ]; then
    print_status 0 "GitHub Actions workflow file exists"
    
    # Check for pnpm version conflicts
    if grep -q "version.*PNPM_VERSION" .github/workflows/ci.yml; then
        print_status 1 "pnpm version conflict detected in workflow"
    else
        print_status 0 "No pnpm version conflicts in workflow"
    fi
    
    # Check for v4 artifact actions
    if grep -q "actions/upload-artifact@v4" .github/workflows/ci.yml; then
        print_status 0 "Using GitHub Actions v4 artifacts"
    else
        print_status 1 "Not using GitHub Actions v4 artifacts"
    fi
    
    # Check for required jobs
    required_jobs=("lint-and-typecheck" "test-node" "test-workers" "security-audit")
    for job in "${required_jobs[@]}"; do
        if grep -q "$job:" .github/workflows/ci.yml; then
            print_status 0 "Job '$job' found"
        else
            print_status 1 "Job '$job' missing"
        fi
    done
else
    print_status 1 "GitHub Actions workflow file missing"
fi

# 3. Check test configuration
echo ""
echo "3. Test Configuration"
echo "--------------------"

if [ -f "vitest.config.ts" ]; then
    print_status 0 "Cloudflare Workers test config exists"
else
    print_status 1 "Cloudflare Workers test config missing"
fi

if [ -f "vitest.node.config.ts" ]; then
    print_status 0 "Node.js test config exists"
else
    print_status 1 "Node.js test config missing"
fi

if [ -f "tests/setup.ts" ]; then
    print_status 0 "Cloudflare Workers test setup exists"
else
    print_status 1 "Cloudflare Workers test setup missing"
fi

if [ -f "tests/setup.node.ts" ]; then
    print_status 0 "Node.js test setup exists"
else
    print_status 1 "Node.js test setup missing"
fi

# 4. Check mock factory
echo ""
echo "4. Mock Factory System"
echo "---------------------"

if [ -f "tests/mocks/external-apis.ts" ]; then
    print_status 0 "Mock factory file exists"
    
    # Check for required mock functions
    required_mocks=("createMockWhatsAppAdapter" "createMockDpoAdapter" "createMockUserService" "createMockPaymentService" "createMockFlowService" "createMockLogger" "TestDataGenerator")
    for mock in "${required_mocks[@]}"; do
        if grep -q "$mock" tests/mocks/external-apis.ts; then
            print_status 0 "Mock '$mock' found"
        else
            print_status 1 "Mock '$mock' missing"
        fi
    done
else
    print_status 1 "Mock factory file missing"
fi

# 5. Check test files
echo ""
echo "5. Test Files Coverage"
echo "---------------------"

test_dirs=("tests/unit/application/services" "tests/unit/infrastructure/adapters" "tests/unit/shared/utils" "tests/unit/presentation/controllers")
for dir in "${test_dirs[@]}"; do
    if [ -d "$dir" ]; then
        test_count=$(find "$dir" -name "*.test.ts" | wc -l)
        if [ $test_count -gt 0 ]; then
            print_status 0 "$dir has $test_count test files"
        else
            print_status 1 "$dir has no test files"
        fi
    else
        print_status 1 "$dir directory missing"
    fi
done

# 6. Check dependencies
echo ""
echo "6. Dependencies Check"
echo "--------------------"

# Check if pnpm is installed
if command -v pnpm &> /dev/null; then
    pnpm_version=$(pnpm --version)
    print_status 0 "pnpm installed (version: $pnpm_version)"
    
    # Check if it matches package.json
    if [ "$pnpm_version" = "9.0.0" ]; then
        print_status 0 "pnpm version matches package.json"
    else
        print_warning "pnpm version ($pnpm_version) doesn't match package.json (9.0.0)"
    fi
else
    print_status 1 "pnpm not installed"
fi

# Check if node_modules exists
if [ -d "node_modules" ]; then
    print_status 0 "Dependencies installed"
else
    print_warning "Dependencies not installed (run 'pnpm install')"
fi

# 7. Run basic tests
echo ""
echo "7. Basic Test Execution"
echo "----------------------"

print_info "Running TypeScript type check..."
if pnpm run type-check > /dev/null 2>&1; then
    print_status 0 "TypeScript type check passed"
else
    print_status 1 "TypeScript type check failed"
fi

print_info "Running linter..."
if pnpm run lint > /dev/null 2>&1; then
    print_status 0 "Linting passed"
else
    print_status 1 "Linting failed"
fi

print_info "Running simple test..."
if pnpm run test tests/simple.test.ts > /dev/null 2>&1; then
    print_status 0 "Simple test passed"
else
    print_status 1 "Simple test failed"
fi

# 8. Summary
echo ""
echo "8. Summary"
echo "----------"

echo -e "${BLUE}📊 CI/CD Setup Verification Complete${NC}"
echo ""
echo -e "${GREEN}✅ Ready for GitHub Actions v4${NC}"
echo -e "${GREEN}✅ pnpm version mismatch resolved${NC}"
echo -e "${GREEN}✅ Comprehensive test coverage setup${NC}"
echo -e "${GREEN}✅ Modern artifact management${NC}"
echo ""

print_info "Next steps:"
echo "  1. Push changes to GitHub to trigger CI/CD pipeline"
echo "  2. Set up repository secrets (CLOUDFLARE_API_TOKEN, CODECOV_TOKEN)"
echo "  3. Monitor first CI/CD run for any remaining issues"
echo "  4. Review coverage reports and adjust thresholds if needed"

echo ""
echo -e "${GREEN}🚀 Your CI/CD setup is ready for production!${NC}"
