# CI/CD Setup Guide

## Overview

This document describes the CI/CD pipeline setup for the WhatsApp Flow application, including the migration to GitHub Actions v4 artifacts and comprehensive testing strategy.

## GitHub Actions Workflow

### Pipeline Structure

The CI/CD pipeline consists of 6 main jobs:

1. **Lint and Type Check** - Code quality validation
2. **Unit Tests (Node.js)** - Coverage testing in Node.js environment
3. **Cloudflare Workers Tests** - Runtime-specific testing
4. **Security Audit** - Dependency and security checks
5. **Build and Deploy** - Production deployment
6. **Coverage Report** - Coverage analysis and reporting

### Key Features

#### ✅ **GitHub Actions v4 Compliance**
- Uses `actions/upload-artifact@v4` and `actions/download-artifact@v4`
- Addresses the deprecation of v3 artifact actions
- Improved artifact handling with better retention policies

#### ✅ **Optimized pnpm Support**
- Proper setup order: pnpm first, then Node.js with `cache: 'pnpm'`
- Built-in pnpm caching eliminates manual cache configuration
- Automatic detection of `pnpm-lock.yaml` for dependency caching

#### ✅ **Dual Testing Strategy**
- **Node.js Environment**: For coverage reporting with `@vitest/coverage-v8`
- **Cloudflare Workers Environment**: For runtime-specific testing

#### ✅ **Comprehensive Coverage**
- 80%+ target coverage with detailed reporting
- Coverage comments on pull requests
- HTML coverage reports as artifacts

#### ✅ **Security Integration**
- Automated dependency auditing
- Security vulnerability scanning
- Codecov integration for coverage tracking

## Configuration Files

### 1. **Main Workflow** (`.github/workflows/ci.yml`)
- Complete CI/CD pipeline
- Artifact management with v4 actions
- Environment-specific deployments
- Coverage reporting and PR comments

### 2. **Node.js Test Config** (`vitest.node.config.ts`)
- Coverage-enabled testing for Node.js
- MSW integration for API mocking
- Comprehensive coverage thresholds

### 3. **Cloudflare Workers Test Config** (`vitest.config.ts`)
- Workers runtime testing
- D1 database integration
- Workers-specific environment setup

### 4. **Test Setup Files**
- `tests/setup.ts` - Cloudflare Workers test setup
- `tests/setup.node.ts` - Node.js test setup with MSW

## Test Coverage Strategy

### Coverage Targets
```
Overall: 80%+
├── Domain Layer: 85%+
├── Application Layer: 75%+
├── Infrastructure Layer: 70%+
├── Presentation Layer: 70%+
└── Shared Layer: 80%+
```

### Coverage Thresholds
```typescript
thresholds: {
  global: { lines: 60, functions: 60, branches: 60, statements: 60 },
  'src/application/': { lines: 70, functions: 70, branches: 70, statements: 70 },
  'src/domain/': { lines: 80, functions: 80, branches: 80, statements: 80 },
  'src/shared/utils/': { lines: 75, functions: 75, branches: 75, statements: 75 },
}
```

## Required Secrets

### GitHub Repository Secrets

1. **Cloudflare Deployment**
   ```
   CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
   CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
   ```

2. **Coverage Reporting**
   ```
   CODECOV_TOKEN=your_codecov_token
   ```

### Environment Variables

The pipeline automatically sets up test environment variables:
```bash
ENVIRONMENT=test
WHATSAPP_VERIFY_TOKEN=test_verify_token
WHATSAPP_ACCESS_TOKEN=test_access_token
# ... additional test variables
```

## Package.json Scripts

### Test Commands
```bash
npm run test                    # Cloudflare Workers tests
npm run test:node              # Node.js tests with coverage
npm run test:coverage          # Workers tests with coverage (limited)
npm run test:coverage:node     # Node.js tests with full coverage
npm run test:watch             # Watch mode
npm run test:cloudflare        # Cloudflare-specific tests
```

### Development Commands
```bash
npm run dev                     # Development server
npm run build                   # Build application
npm run deploy                  # Deploy to Cloudflare
npm run lint                    # ESLint
npm run type-check             # TypeScript validation
```

### Database Commands
```bash
npm run db:generate            # Generate migrations
npm run db:migrate             # Apply migrations (production)
npm run db:migrate:local       # Apply migrations (local)
npm run db:studio              # Drizzle Studio
```

## Artifact Management

### Artifact Types

1. **Coverage Reports** (`coverage-reports`)
   - HTML coverage reports
   - LCOV files for Codecov
   - JSON coverage summaries
   - Retention: 30 days

2. **Build Artifacts** (`build-artifacts`)
   - Compiled application
   - Configuration files
   - Retention: 90 days

3. **Test Results** (`workers-test-results`)
   - Test output logs
   - Error reports
   - Retention: 7 days

### Artifact Usage

#### Download Coverage Reports
```bash
# In GitHub Actions
- name: Download coverage artifacts
  uses: actions/download-artifact@v4
  with:
    name: coverage-reports
    path: coverage/
```

#### Upload Build Artifacts
```bash
# In GitHub Actions
- name: Upload build artifacts
  uses: actions/upload-artifact@v4
  with:
    name: build-artifacts
    path: |
      dist/
      wrangler.toml
    retention-days: 90
```

## Coverage Reporting

### Pull Request Comments

The pipeline automatically comments on PRs with coverage information:

```markdown
## 📊 Test Coverage Report

| Metric | Percentage | Status |
|--------|------------|--------|
| Lines | 85.2% | ✅ |
| Functions | 82.1% | ✅ |
| Branches | 78.9% | ✅ |
| Statements | 84.7% | ✅ |

**Target:** 80% coverage for lines, functions, and statements; 75% for branches
```

### Coverage Integration

- **Codecov**: Automatic upload of coverage reports
- **GitHub Summary**: Coverage summary in workflow runs
- **HTML Reports**: Downloadable detailed coverage reports

## Deployment Strategy

### Environments

1. **Development**: Automatic deployment from `develop` branch
2. **Production**: Manual deployment from `main` branch
3. **Feature Branches**: Test-only (no deployment)

### Deployment Process

1. All tests must pass
2. Security audit must pass
3. Coverage thresholds must be met
4. Manual approval for production
5. Automatic rollback on failure

## Troubleshooting

### Common Issues

1. **Coverage Tool Limitations**
   - `@vitest/coverage-v8` doesn't work in Cloudflare Workers
   - Solution: Use Node.js environment for coverage

2. **Artifact Upload Failures**
   - Ensure using `actions/upload-artifact@v4`
   - Check file paths and retention settings

3. **Test Environment Issues**
   - Verify environment variables are set
   - Check D1 database configuration

### Debug Commands

```bash
# Local debugging
npm run test:debug              # Verbose test output
npm run test:coverage:node      # Local coverage
npm run lint:fix                # Fix linting issues
npm run type-check              # Check TypeScript
```

## Migration from v3 to v4 Artifacts

### Changes Made

1. **Updated Action Versions**
   ```yaml
   # Before
   uses: actions/upload-artifact@v3
   
   # After
   uses: actions/upload-artifact@v4
   ```

2. **Improved Retention Policies**
   ```yaml
   retention-days: 30  # Explicit retention
   ```

3. **Better Path Handling**
   ```yaml
   path: |
     coverage/
     !coverage/tmp/  # Exclude temporary files
   ```

### Benefits

- ✅ Future-proof artifact handling
- ✅ Better performance and reliability
- ✅ Improved artifact organization
- ✅ Enhanced security features

## Next Steps

1. **Set up repository secrets** for Cloudflare and Codecov
2. **Configure branch protection** rules requiring CI checks
3. **Set up monitoring** for deployment health
4. **Configure notifications** for build failures
5. **Review and adjust** coverage thresholds as needed

The CI/CD pipeline is now ready for production use with modern GitHub Actions v4 artifacts and comprehensive testing coverage! 🚀
