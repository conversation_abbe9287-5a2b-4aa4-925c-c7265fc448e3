# Testing Documentation

## Overview

This document describes the comprehensive testing framework for the WhatsApp Flow application. The testing suite achieves over 60% code coverage across all application layers and includes unit tests, integration tests, and end-to-end testing scenarios.

## Testing Framework

### Technology Stack

- **Test Runner**: Vitest v2.1.8
- **Coverage**: @vitest/coverage-v8
- **Mocking**: <PERSON><PERSON> (Mock Service Worker) for API mocking
- **Database**: better-sqlite3 for in-memory testing
- **Environment**: happy-dom for DOM simulation
- **Assertions**: Vitest built-in assertions

### Configuration

The testing framework is configured in `vitest.config.ts`:

```typescript
export default defineConfig({
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['./tests/setup.ts'],
    include: ['tests/**/*.test.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      thresholds: {
        global: { branches: 60, functions: 60, lines: 60, statements: 60 },
        'src/application/': { branches: 70, functions: 70, lines: 70, statements: 70 },
        'src/domain/': { branches: 80, functions: 80, lines: 80, statements: 80 },
      },
    },
  },
});
```

## Test Structure

### Directory Organization

```
tests/
├── setup.ts                    # Global test setup
├── utils/
│   └── test-helpers.ts         # Test utilities and helpers
├── mocks/
│   └── external-apis.ts        # Mock factories for external services
├── unit/                       # Unit tests
│   ├── shared/
│   │   ├── utils/             # Utility function tests
│   │   └── middleware/        # Middleware tests
│   ├── application/
│   │   └── services/          # Service layer tests
│   ├── infrastructure/
│   │   ├── adapters/          # External adapter tests
│   │   └── repositories/      # Repository tests
│   └── presentation/
│       └── controllers/       # Controller tests
├── integration/                # Integration tests
│   ├── api/                   # API endpoint tests
│   └── database/              # Database operation tests
└── sample-payloads/           # Test data and payloads
```

## Running Tests

### Basic Commands

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run with UI interface
pnpm test:ui

# Generate coverage report
pnpm test:coverage

# Run coverage in watch mode
pnpm test:coverage:watch

# Run only unit tests
pnpm test:unit

# Run only integration tests
pnpm test:integration

# Run tests for CI/CD
pnpm test:ci
```

### Coverage Reports

Coverage reports are generated in multiple formats:

- **Terminal**: Real-time coverage summary
- **HTML**: Interactive coverage report in `./coverage/index.html`
- **JSON**: Machine-readable coverage data in `./coverage/coverage.json`
- **LCOV**: For integration with external tools in `./coverage/lcov.info`

## Test Categories

### 1. Unit Tests

Unit tests focus on individual components in isolation with mocked dependencies.

#### Utilities Tests (`tests/unit/shared/utils/`)

- **CryptoUtils**: Encryption, decryption, HMAC signatures, UUID generation
- **Validation**: Input validation, schema validation, sanitization
- **Logger**: Logging functionality and log levels

#### Service Tests (`tests/unit/application/services/`)

- **UserService**: User creation, updates, registration completion
- **PaymentService**: Payment processing, status updates, DPO integration
- **FlowService**: WhatsApp Flow session management
- **WhatsAppService**: Message handling and flow orchestration

#### Adapter Tests (`tests/unit/infrastructure/adapters/`)

- **WhatsAppAdapter**: API communication, message sending, error handling
- **DpoAdapter**: Payment token creation, verification, cancellation

#### Middleware Tests (`tests/unit/shared/middleware/`)

- **ErrorHandler**: Error processing, status codes, logging
- **RateLimiter**: Request limiting, database tracking, cleanup
- **Security**: Headers, validation, access control

### 2. Integration Tests

Integration tests verify component interactions and external service integration.

#### API Tests (`tests/integration/api/`)

- **WhatsApp Webhook**: Verification, message processing, signature validation
- **Payment Callbacks**: DPO webhook handling, status updates

#### Database Tests (`tests/integration/database/`)

- **UserRepository**: CRUD operations, constraints, concurrent access
- **PaymentRepository**: Payment tracking, status updates, statistics
- **FlowSessionRepository**: Session management, cleanup, expiration

### 3. Controller Tests

Controller tests verify HTTP request/response handling with mocked services.

- **WhatsAppController**: Webhook verification, event processing, health checks
- **PaymentController**: Callback handling, status pages, API endpoints

## Test Data and Mocking

### Mock Factories

The testing framework includes comprehensive mock factories:

```typescript
// External API mocks
export const createMockWhatsAppAdapter = () => ({
  sendTextMessage: vi.fn().mockResolvedValue({ messageId: 'test_message_id' }),
  sendInteractiveMessage: vi.fn().mockResolvedValue({ messageId: 'test_message_id' }),
  // ... other methods
});

// Service mocks
export const createMockUserRepository = () => ({
  create: vi.fn(),
  findById: vi.fn(),
  // ... other methods
});
```

### Test Data Generation

Realistic test data is generated using the `TestDataGenerator` class:

```typescript
export class TestDataGenerator {
  static generateId(): string;
  static generatePhoneNumber(): string;
  static generateEmail(): string;
  static generateName(): string;
  static generateAmount(): number;
}
```

### Sample Payloads

Pre-defined test payloads are available for common scenarios:

- WhatsApp webhook verification
- Text message webhooks
- Flow response webhooks
- DPO payment callbacks

## Coverage Targets

### Global Coverage Requirements

- **Minimum**: 60% across all metrics (lines, functions, branches, statements)
- **Application Layer**: 70% (business logic)
- **Domain Layer**: 80% (core entities)
- **Utilities**: 75% (shared functionality)

### Current Coverage Areas

1. **High Coverage (>80%)**:
   - Domain entities and validation
   - Utility functions (crypto, validation)
   - Core business services

2. **Good Coverage (60-80%)**:
   - Controllers and middleware
   - Repository implementations
   - External adapters

3. **Focus Areas for Improvement**:
   - Error handling edge cases
   - Concurrent operation scenarios
   - Network failure recovery

## Best Practices

### Test Writing Guidelines

1. **Descriptive Names**: Use "should [expected behavior] when [condition]" pattern
2. **Arrange-Act-Assert**: Structure tests clearly
3. **Single Responsibility**: One assertion per test when possible
4. **Mock External Dependencies**: Isolate units under test
5. **Test Edge Cases**: Include error scenarios and boundary conditions

### Example Test Structure

```typescript
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user successfully when valid data provided', async () => {
      // Arrange
      const userData = { whatsappPhoneNumber: '+1234567890' };
      mockRepository.findByWhatsAppPhone.mockResolvedValue(null);
      mockRepository.create.mockResolvedValue(expectedUser);

      // Act
      const result = await userService.createUser(userData);

      // Assert
      expect(result).toEqual(expectedUser);
      expect(mockRepository.create).toHaveBeenCalledWith(userData);
    });

    it('should throw error when invalid phone number provided', async () => {
      // Arrange
      const userData = { whatsappPhoneNumber: 'invalid' };

      // Act & Assert
      await expect(userService.createUser(userData)).rejects.toThrow('Invalid phone number format');
    });
  });
});
```

## Continuous Integration

### CI/CD Integration

The test suite is designed for CI/CD integration:

```bash
# CI command with coverage and reporting
pnpm test:ci
```

This command:
- Runs all tests once (no watch mode)
- Generates coverage reports
- Outputs JUnit XML for CI integration
- Uses verbose reporting for detailed logs

### GitHub Actions Example

```yaml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm test:ci
      - uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

## Debugging Tests

### Common Issues and Solutions

1. **Database State**: Tests failing due to shared state
   - Solution: Ensure proper cleanup in `beforeEach`

2. **Async Operations**: Race conditions in tests
   - Solution: Use proper `await` and `vi.waitFor()`

3. **Mock Persistence**: Mocks not resetting between tests
   - Solution: Use `vi.clearAllMocks()` in setup

4. **External API Calls**: Tests making real API calls
   - Solution: Verify MSW handlers are properly configured

### Debug Commands

```bash
# Run specific test file
pnpm test tests/unit/application/services/UserService.test.ts

# Run tests with debug output
DEBUG=* pnpm test

# Run single test with watch mode
pnpm test:watch --reporter=verbose
```

## Performance Considerations

### Test Execution Speed

- **Parallel Execution**: Tests run in parallel by default
- **In-Memory Database**: SQLite in-memory for fast database tests
- **Mock External APIs**: No real network calls during testing
- **Selective Testing**: Run only changed tests during development

### Resource Management

- **Database Cleanup**: Automatic cleanup between tests
- **Memory Usage**: Proper mock cleanup prevents memory leaks
- **File Handles**: Proper resource disposal in teardown

## Maintenance

### Regular Tasks

1. **Update Test Data**: Keep sample payloads current with API changes
2. **Review Coverage**: Monitor coverage trends and identify gaps
3. **Mock Updates**: Update mocks when external APIs change
4. **Performance Monitoring**: Track test execution time

### Adding New Tests

When adding new features:

1. Write tests first (TDD approach)
2. Ensure coverage meets thresholds
3. Add integration tests for new endpoints
4. Update mock factories as needed
5. Document any new testing patterns

## Troubleshooting

### Common Test Failures

1. **Timeout Errors**: Increase timeout in vitest config
2. **Database Locks**: Ensure proper connection cleanup
3. **Mock Conflicts**: Check for conflicting MSW handlers
4. **Coverage Drops**: Add tests for new code paths

### Getting Help

- Check test logs for detailed error messages
- Use `--reporter=verbose` for detailed output
- Review coverage reports to identify untested code
- Consult the test utilities for helper functions
