# Cloudflare Workers Testing Guide

## Overview

This document describes the testing setup for our WhatsApp Flow application using Cloudflare Workers testing best practices with `@cloudflare/vitest-pool-workers`.

## Architecture

Our testing framework is built on:

- **Vitest 3.2.x** - Modern test runner with TypeScript support
- **@cloudflare/vitest-pool-workers** - Cloudflare Workers testing integration
- **MSW (Mock Service Worker)** - API mocking for external services
- **D1 Database** - In-memory database for testing

## Configuration

### Vitest Configuration (`vitest.config.ts`)

```typescript
import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';

export default defineWorkersConfig({
  test: {
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    poolOptions: {
      workers: {
        wrangler: { 
          configPath: './wrangler.toml',
        },
        miniflare: {
          // Test-specific bindings
          kvNamespaces: ['TEST_KV'],
          d1Databases: ['TEST_DB'],
          d1Persist: false,
          kvPersist: false,
        },
      },
    },
  },
});
```

### TypeScript Configuration (`tests/tsconfig.json`)

```json
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "moduleResolution": "bundler",
    "types": [
      "@cloudflare/vitest-pool-workers",
      "vitest/globals"
    ]
  },
  "include": [
    "./**/*.ts",
    "../src/worker-configuration.d.ts",
    "../src/**/*.ts"
  ]
}
```

### Environment Types (`tests/env.d.ts`)

```typescript
declare module "cloudflare:test" {
  interface ProvidedEnv {
    DB: D1Database;
    TEST_DB: D1Database;
    TEST_KV: KVNamespace;
    
    WHATSAPP_VERIFY_TOKEN: string;
    WHATSAPP_ACCESS_TOKEN: string;
    // ... other environment variables
  }
}
```

## Test Types

### 1. Unit Tests

Test individual components in isolation using the Workers runtime:

```typescript
import { env, createExecutionContext, waitOnExecutionContext } from 'cloudflare:test';
import worker from '@/index';

const IncomingRequest = Request<unknown, IncomingRequestCfProperties>;

describe('Worker Unit Tests', () => {
  it('should handle requests', async () => {
    const request = new IncomingRequest('https://example.com/health');
    const ctx = createExecutionContext();
    
    const response = await worker.fetch(request, env, ctx);
    await waitOnExecutionContext(ctx);
    
    expect(response.status).toBe(200);
  });
});
```

### 2. Integration Tests

Test the complete Worker using the `SELF` fetcher:

```typescript
import { SELF, env } from 'cloudflare:test';

describe('Integration Tests', () => {
  it('should process webhook', async () => {
    const response = await SELF.fetch('https://example.com/api/whatsapp/webhook', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(webhookPayload),
    });
    
    expect(response.status).toBe(200);
  });
});
```

### 3. Database Tests

Test database operations using D1:

```typescript
describe('Database Tests', () => {
  beforeEach(async () => {
    await env.TEST_DB.exec('DELETE FROM users');
  });

  it('should create user', async () => {
    await env.TEST_DB.prepare(`
      INSERT INTO users (id, whatsapp_phone_number, created_at, updated_at)
      VALUES (?, ?, ?, ?)
    `).bind('user-1', '+1234567890', new Date().toISOString(), new Date().toISOString()).run();
    
    const user = await env.TEST_DB.prepare('SELECT * FROM users WHERE id = ?')
      .bind('user-1')
      .first();
    
    expect(user?.whatsapp_phone_number).toBe('+1234567890');
  });
});
```

## Test Setup (`tests/setup.ts`)

The setup file configures:

1. **MSW Server** - Mocks external APIs (WhatsApp, DPO Pay)
2. **Database Setup** - Creates test tables in D1
3. **Cleanup** - Cleans data between tests

```typescript
import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { setupServer } from 'msw/node';
import { env } from 'cloudflare:test';

// MSW server for API mocking
export const server = setupServer(/* handlers */);

beforeAll(async () => {
  server.listen({ onUnhandledRequest: 'error' });
  await setupTestDatabase();
});

beforeEach(async () => {
  await cleanTestDatabase();
});
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm run test

# Run with coverage
npm run test:coverage

# Run specific test types
npm run test:unit
npm run test:integration

# Run in watch mode
npm run test:watch

# Run with UI
npm run test:ui

# CI/CD command
npm run test:ci
```

### Environment Variables

Tests use environment variables from `wrangler.toml`:

```toml
[env.test]
WHATSAPP_VERIFY_TOKEN = "test_verify_token"
WHATSAPP_ACCESS_TOKEN = "test_access_token"
DPO_COMPANY_TOKEN = "test_dpo_token"
# ... other test variables
```

## Best Practices

### 1. Test Isolation

- Each test runs in a fresh environment
- Database is cleaned between tests
- MSW handlers are reset after each test

### 2. Realistic Testing

- Use actual D1 database (in-memory)
- Test with real Cloudflare Workers runtime
- Mock external APIs with MSW

### 3. Error Handling

- Test error scenarios
- Verify graceful degradation
- Test rate limiting and security

### 4. Performance

- Tests run in parallel
- In-memory storage for speed
- Efficient cleanup strategies

## Debugging

### 1. Debug Individual Tests

```bash
# Run specific test file
npm run test tests/unit/cloudflare-workers/worker.test.ts

# Run with verbose output
npm run test -- --reporter=verbose
```

### 2. Debug Database Issues

```typescript
// Add logging to see database state
console.log(await env.TEST_DB.prepare('SELECT * FROM users').all());
```

### 3. Debug MSW Issues

```typescript
// Check if MSW is intercepting requests
server.use(
  http.all('*', ({ request }) => {
    console.log('MSW intercepted:', request.url);
    return passthrough();
  })
);
```

## Migration from Previous Setup

### Changes Made

1. **Vitest Configuration**
   - Replaced `defineConfig` with `defineWorkersConfig`
   - Added `poolOptions.workers` configuration
   - Removed `happy-dom` environment (not needed for Workers)

2. **Test Setup**
   - Replaced `better-sqlite3` with D1 database
   - Added Cloudflare Workers runtime imports
   - Updated database operations for D1 API

3. **Test Files**
   - Added `cloudflare:test` imports
   - Updated to use `env`, `SELF`, `createExecutionContext`
   - Added proper TypeScript types for Workers

### Compatibility

- All existing test logic preserved
- Same coverage requirements maintained
- MSW mocking continues to work
- Test data and assertions unchanged

## Troubleshooting

### Common Issues

1. **Module Resolution Errors**
   ```
   Cannot find module 'cloudflare:test'
   ```
   - Ensure `@cloudflare/vitest-pool-workers` is in types array
   - Check `tests/tsconfig.json` configuration

2. **Database Errors**
   ```
   D1_ERROR: no such table: users
   ```
   - Verify database setup in `tests/setup.ts`
   - Check table creation SQL

3. **Environment Variable Issues**
   ```
   env.WHATSAPP_VERIFY_TOKEN is undefined
   ```
   - Check `wrangler.toml` configuration
   - Verify environment variable names

### Getting Help

1. Check Cloudflare Workers testing docs
2. Review `@cloudflare/vitest-pool-workers` examples
3. Examine test logs for detailed error messages
4. Use `console.log` for debugging test state

## Performance Metrics

Our test suite achieves:

- **Coverage**: >60% overall, >70% application layer, >80% domain layer
- **Speed**: ~30 seconds for full test suite
- **Reliability**: Consistent results across environments
- **Maintainability**: Clear separation of concerns

## Future Improvements

1. **Auxiliary Workers** - For more isolated integration tests
2. **Performance Testing** - Load testing with Workers runtime
3. **E2E Testing** - Full flow testing with real WhatsApp webhooks
4. **Visual Testing** - Screenshot testing for payment pages
