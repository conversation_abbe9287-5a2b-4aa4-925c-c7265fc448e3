# API Documentation

## Overview

This document describes the API endpoints for the WhatsApp Flow Application.

## Base URL

- **Development**: `http://localhost:8787`
- **Production**: `https://your-worker-domain.workers.dev`

## Authentication

Most endpoints are public webhooks. Some development endpoints may require API keys in production.

## Endpoints

### Health Checks

#### GET /
Main application health check.

**Response:**
```json
{
  "status": "healthy",
  "service": "whatsapp-flows-hono",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

#### GET /health
WhatsApp service health check.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "whatsapp-flows-hono"
}
```

#### GET /health/payment
Payment service health check with statistics.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "payment-service",
  "stats": {
    "total": 100,
    "completed": 85,
    "pending": 10,
    "failed": 5,
    "totalAmount": 9999.99,
    "completedAmount": 8499.99
  }
}
```

### WhatsApp Webhooks

#### GET /webhook/whatsapp
WhatsApp webhook verification endpoint.

**Query Parameters:**
- `hub.mode` (string): Should be "subscribe"
- `hub.verify_token` (string): Your verification token
- `hub.challenge` (string): Challenge string to echo back

**Response:**
- **200**: Returns the challenge string
- **403**: Invalid verification token

#### POST /webhook/whatsapp
WhatsApp webhook event handler.

**Headers:**
- `Content-Type: application/json`
- `X-Hub-Signature-256: sha256=<signature>` (optional, for verification)

**Request Body:**
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "PHONE_NUMBER_ID"
            },
            "messages": [
              {
                "from": "***********",
                "id": "message_id",
                "timestamp": "**********",
                "type": "text",
                "text": {
                  "body": "Hello"
                }
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

**Response:**
- **200**: "OK"
- **400**: Invalid payload
- **401**: Invalid signature
- **500**: Internal server error

#### GET /webhook/whatsapp/info
Get webhook configuration info (development only).

**Response:**
```json
{
  "webhookUrl": "https://your-worker-domain.workers.dev/webhook/whatsapp",
  "verifyToken": "configured",
  "webhookSecret": "configured",
  "phoneNumberId": "PHONE_NUMBER_ID",
  "environment": "development"
}
```

### Payment Webhooks

#### POST /webhook/dpo
DPO Pay payment callback handler.

**Headers:**
- `Content-Type: application/x-www-form-urlencoded`

**Request Body (URL-encoded):**
```
TransactionToken=ABC123&CompanyRef=payment-123&Result=000&ResultExplanation=Transaction+Paid&TransactionRef=DPO123&TransactionAmount=99.99&TransactionCurrency=USD&CustomerEmail=<EMAIL>&CustomerPhone=+1234567890
```

**Response:**
- **200**: "OK"
- **400**: Missing transaction token
- **500**: Internal server error

### Payment Callbacks

#### GET /payment/callback
Payment success redirect page.

**Query Parameters:**
- `TransactionToken` (string): DPO transaction token
- `CompanyRef` (string): Company reference

**Response:**
- **200**: HTML success page
- **404**: Payment not found
- **500**: Processing error

#### GET /payment/cancel
Payment cancellation redirect page.

**Query Parameters:**
- `TransactionToken` (string): DPO transaction token (optional)

**Response:**
- **200**: HTML cancellation page

### API Routes

#### GET /api/payment/methods
Get available payment methods.

**Response:**
```json
{
  "paymentMethods": [
    {
      "id": "card",
      "name": "Credit/Debit Card",
      "description": "Visa, Mastercard, American Express"
    },
    {
      "id": "mobile",
      "name": "Mobile Money",
      "description": "M-Pesa, Airtel Money, etc."
    }
  ]
}
```

#### GET /api/payment/:paymentId/status
Get payment status by ID (development only).

**Path Parameters:**
- `paymentId` (string): Payment ID

**Response:**
```json
{
  "id": "payment-123",
  "status": "completed",
  "amount": 99.99,
  "currency": "USD",
  "productName": "Premium Plan",
  "dpoTransactionToken": "ABC123",
  "dpoPaymentReference": "DPO123",
  "paymentUrl": "https://secure.3gdirectpay.com/?ID=ABC123",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:05:00.000Z"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint"
}
```

### Error Codes

- `VALIDATION_ERROR` (400): Invalid input data
- `UNAUTHORIZED` (401): Missing or invalid authentication
- `FORBIDDEN` (403): Access denied
- `NOT_FOUND` (404): Resource not found
- `CONFLICT` (409): Resource conflict
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `EXTERNAL_SERVICE_ERROR` (502): External service failure
- `INTERNAL_SERVER_ERROR` (500): Internal server error

## Rate Limiting

API endpoints are rate limited:

- **Default**: 60 requests per minute
- **Burst**: 10 requests
- **Window**: 1 minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Requests per minute limit
- `X-RateLimit-Remaining`: Remaining requests in window
- `X-RateLimit-Reset`: Unix timestamp when window resets

## Security Headers

All responses include security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Content-Security-Policy: ...`
- `Strict-Transport-Security: ...` (production only)

## Webhook Signature Verification

WhatsApp webhooks can be verified using HMAC-SHA256:

1. Get the signature from `X-Hub-Signature-256` header
2. Remove the `sha256=` prefix
3. Compute HMAC-SHA256 of the request body using your webhook secret
4. Compare the computed signature with the received signature

Example verification (Node.js):
```javascript
const crypto = require('crypto');

function verifySignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}
```
