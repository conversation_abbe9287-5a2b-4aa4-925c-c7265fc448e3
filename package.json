{"name": "whatsapp-flows-hono", "version": "1.0.0", "description": "Production-ready WhatsApp Flow application with Cloudflare Workers", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "build": "wrangler deploy --dry-run", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:generate": "drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply whatsapp-flows-db", "db:migrate:local": "wrangler d1 migrations apply whatsapp-flows-db --local", "db:studio": "drizzle-kit studio", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --config vitest.config.ci.ts --coverage", "test:coverage:watch": "vitest --config vitest.config.ci.ts --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:cloudflare": "vitest run tests/unit/cloudflare-workers tests/integration/cloudflare-workers", "test:ci": "vitest run --config vitest.config.ci.ts --coverage --reporter=verbose --reporter=junit --outputFile=test-results.xml", "test:node": "vitest run", "test:debug": "vitest run --reporter=verbose", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "deps:check": "pnpm outdated"}, "dependencies": {"@kazion/dpopay-sdk": "^4.0.18", "crypto-js": "^4.2.0", "drizzle-orm": "^0.44.2", "hono": "^4.8.2", "zod": "^3.25.67"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.43", "@cloudflare/workers-types": "^4.20250620.0", "@eslint/js": "^9.29.0", "@types/better-sqlite3": "^7.6.13", "@types/crypto-js": "^4.2.2", "@types/node": "^24.0.3", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "better-sqlite3": "^12.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "happy-dom": "^18.0.1", "jiti": "^2.4.2", "msw": "^2.10.2", "supertest": "^7.1.1", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vitest": "~3.2.0", "wrangler": "^4.20.5"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18.0.0"}, "pnpm": {"overrides": {"esbuild": ">=0.25.0"}}}