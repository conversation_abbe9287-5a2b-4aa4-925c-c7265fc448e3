-- Create users table
CREATE TABLE `users` (
	`id` text PRIMARY KEY NOT NULL,
	`whatsapp_phone_number` text NOT NULL,
	`name` text,
	`email` text,
	`registration_status` text DEFAULT 'pending' NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create payments table
CREATE TABLE `payments` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`dpo_transaction_token` text NOT NULL,
	`dpo_payment_reference` text,
	`amount` real NOT NULL,
	`currency` text NOT NULL,
	`status` text DEFAULT 'pending' NOT NULL,
	`product_name` text NOT NULL,
	`product_description` text,
	`customer_email` text NOT NULL,
	`customer_phone` text NOT NULL,
	`payment_url` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`completed_at` text,
	`failed_at` text,
	`failure_reason` text,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);

-- Create flow_sessions table
CREATE TABLE `flow_sessions` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`flow_type` text NOT NULL,
	`flow_id` text NOT NULL,
	`status` text DEFAULT 'active' NOT NULL,
	`current_step` text NOT NULL,
	`session_data` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`completed_at` text,
	`expires_at` text NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);

-- Create audit_logs table
CREATE TABLE `audit_logs` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text,
	`action` text NOT NULL,
	`entity_type` text NOT NULL,
	`entity_id` text NOT NULL,
	`old_values` text,
	`new_values` text,
	`metadata` text,
	`ip_address` text,
	`user_agent` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);

-- Create rate_limits table
CREATE TABLE `rate_limits` (
	`id` text PRIMARY KEY NOT NULL,
	`identifier` text NOT NULL,
	`endpoint` text NOT NULL,
	`request_count` integer DEFAULT 0 NOT NULL,
	`window_start` text NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create unique constraints
CREATE UNIQUE INDEX `users_whatsapp_phone_number_unique` ON `users` (`whatsapp_phone_number`);

-- Create indexes for better performance
CREATE INDEX `idx_users_phone` ON `users`(`whatsapp_phone_number`);
CREATE INDEX `idx_payments_user` ON `payments`(`user_id`);
CREATE INDEX `idx_payments_status` ON `payments`(`status`);
CREATE INDEX `idx_flow_sessions_user` ON `flow_sessions`(`user_id`);
CREATE INDEX `idx_flow_sessions_status` ON `flow_sessions`(`status`);
CREATE INDEX `idx_audit_logs_entity` ON `audit_logs`(`entity_type`, `entity_id`);
CREATE INDEX `idx_rate_limits_identifier` ON `rate_limits`(`identifier`, `endpoint`);
