-- Seed data for testing

-- Insert test users
INSERT INTO users (id, whatsapp_phone_number, name, email, registration_status, created_at, updated_at) VALUES
('user-1', '+1234567890', '<PERSON>', '<EMAIL>', 'completed', datetime('now'), datetime('now')),
('user-2', '+1234567891', '<PERSON>', '<EMAIL>', 'pending', datetime('now'), datetime('now')),
('user-3', '+1234567892', '<PERSON>', '<EMAIL>', 'completed', datetime('now'), datetime('now'));

-- Insert test payments
INSERT INTO payments (id, user_id, dpo_transaction_token, amount, currency, status, product_name, customer_email, customer_phone, created_at, updated_at) VALUES
('payment-1', 'user-1', 'dpo-token-1', 99.99, 'USD', 'completed', 'Premium Subscription', '<EMAIL>', '+1234567890', datetime('now'), datetime('now')),
('payment-2', 'user-2', 'dpo-token-2', 49.99, 'USD', 'pending', 'Basic Plan', '<EMAIL>', '+1234567891', datetime('now'), datetime('now')),
('payment-3', 'user-3', 'dpo-token-3', 199.99, 'USD', 'failed', 'Enterprise Plan', '<EMAIL>', '+1234567892', datetime('now'), datetime('now'));

-- Insert test flow sessions
INSERT INTO flow_sessions (id, user_id, flow_type, flow_id, status, current_step, session_data, created_at, updated_at, expires_at) VALUES
('session-1', 'user-1', 'customer_registration', 'flow-reg-1', 'completed', 'complete', '{"name":"John Doe","email":"<EMAIL>"}', datetime('now'), datetime('now'), datetime('now', '+30 minutes')),
('session-2', 'user-2', 'payment', 'flow-pay-1', 'active', 'payment_details', '{"product":"Basic Plan","amount":49.99}', datetime('now'), datetime('now'), datetime('now', '+30 minutes')),
('session-3', 'user-3', 'customer_registration', 'flow-reg-2', 'expired', 'email_verification', '{"name":"Bob Johnson"}', datetime('now'), datetime('now'), datetime('now', '-5 minutes'));

-- Insert test audit logs
INSERT INTO audit_logs (id, user_id, action, entity_type, entity_id, new_values, created_at) VALUES
('audit-1', 'user-1', 'CREATE', 'user', 'user-1', '{"whatsapp_phone_number":"+1234567890","name":"John Doe","email":"<EMAIL>"}', datetime('now')),
('audit-2', 'user-2', 'CREATE', 'payment', 'payment-2', '{"amount":49.99,"currency":"USD","product_name":"Basic Plan"}', datetime('now')),
('audit-3', 'user-3', 'UPDATE', 'payment', 'payment-3', '{"status":"failed","failure_reason":"Insufficient funds"}', datetime('now'));
